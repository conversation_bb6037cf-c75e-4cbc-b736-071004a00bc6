<template>
  <view>
    <Search :searchType="0" @search-result="getSearchResult" v-if="isShowSearch" />
    <goodsList :selectGoods="true" :type="type" :searchResults="searchResults" :relatedOrderGoods="relatedOrderGoods"
      :deselectItemList="deselectItemList" :deselectItem="deselectItem" :warehouseData="warehouseData"
      @selectedGoodsList="selectedGoodsList" @selectType="selectType" />

    <!-- 遮罩层 -->
    <view v-if="shoppingCartShow" class="cart_mask" @click="expandShoppingCart"></view>

    <!-- 购物车面板 -->
    <view v-if="shoppingCartShow" class="cart_popup" :animation="cartAnimation">
      <view class="cart_container">
        <view class="categoryConfirmationBtn">
          <view>已选商品</view>
          <view class="blueFont" @click="clearSelectGoodsList">清空</view>
        </view>
        <view class="divider"></view>
        <view class="goods">
          <view class="goods_item" v-for="(item, index) in selectGoodsList" :key="index">
            <view class="goods_left">
              <image class="goods_img" :src="item.imgurl || '/static/img/logo.png'" mode=""></image>
              <view class="goods_info">
                <view class="goods_extra">
                  {{ item.name }}
                  <view class="stock-tag" v-if="type == 6">
                    库存：{{ Math.floor(item.total_stock) }} - {{ Math.floor(item.remaining_stock) }}
                  </view>
                </view>
                <view class="goods_code">{{ item.code }}</view>
                <view class="goods_price">
                  <text class="price">￥{{ item.purchase_price || "未填写" }}</text>
                  <text class="unit">/{{ item.unit_name || "未填写" }}</text>
                </view>
              </view>
            </view>
            <view class="goods_num">
              <view class="goods_num_reduce" @click.stop="reduceNum(item)">
                <i-reduce-one theme="outline" size="20" fill="#3894ff" />
              </view>
              <view class="goods_num_input">{{ item.quantity || 0 }}</view>
              <view class="goods_num_add" @click.stop="addNum(item)">
                <i-add-one theme="filled" size="20" fill="#3894ff" />
              </view>
            </view>
          </view>
        </view>
      </view>
      <!-- 操作按钮（展开时） -->
      <view class="operatingButton popup-btn">
        <view class="selectGoods">
          <u-button type="warning" @click="expandShoppingCart">
            <i-shopping theme="outline" size="24" fill="#fff" />
            已选择({{ selectGoodsList.length || 0 }})
          </u-button>
        </view>
        <view class="selectGoods">
          <u-button type="primary" @click="confirmSelectGoods">
            <i-check-small theme="outline" size="24" fill="#fff" />选好了
          </u-button>
        </view>
      </view>
      <Input style="height: 100%" />
    </view>

    <!-- 底部操作按钮（收缩时） -->
    <view v-if="!shoppingCartShow" class="operatingButton fixed-btn">
      <view class="selectGoods">
        <u-button type="warning" @click="expandShoppingCart">
          <i-shopping theme="outline" size="24" fill="#fff" />
          已选择({{ selectGoodsList.length || 0 }})
        </u-button>
      </view>
      <view class="selectGoods">
        <u-button type="primary" @click="confirmSelectGoods">
          <i-check-small theme="outline" size="24" fill="#fff" />选好了
        </u-button>
      </view>
    </view>

    <Input style="height: 100%" v-if="!shoppingCartShow" />

    <ProductDetails :selectGoodsList="selectGoodsList" :selectedGoodsQuantity="selectedGoodsQuantity" />
  </view>
</template>
<script>
import Input from "@/components/input/input.vue";
import goodsList from "@/components/goodsList.vue";
import productDetails from "@/components/productDetails.vue";
import Search from "@/components/search.vue";
import eventBus from "@/utils/eventBus";

export default {
  components: {
    Input,
    goodsList,
    Search,
    productDetails,
  },
  data() {
    return {
      relatedOrderGoods: [], //关联订单商品
      selectGoodsList: [], //选择的商品列表
      searchResults: [], //搜索内容
      deselectItem: {}, //取消选择商品
      deselectItemList: [], //取消选择商品列表
      shoppingCartShow: false,
      cartAnimation: null,
      isShowWarehouse: null,//在商品列表是否显示仓库字段
      warehouseData: {
        warehouse: '',//仓库id
        warehouse_name: '',//仓库名称
      },
      type: 0,
      /* 判断进货关联订单(0) 
        任意商品(1) 
        采购订单 || 销售订单(2)
        退货:关联进货单进入(4)
        退货：任意商品进入(5)
        零售订单(6)
        零售退货订单：关联订单进入(7)
        零售退货订单:任意商品进入(8)
      */
      isShowSearch: false, //是否选择商品

    };
  },
  onLoad(options) {
    // 接收type
    console.log(options);
    if (options.type == 5){
      console.log('productSelection 接收到的 options:',options);
      console.log('接收到的仓库信息:', {
        warehouse: options.warehouse,
        warehouse_name: options.warehouse_name
      });
    }
    if (options.type) {
      this.type = Number(options.type);
      if (this.type == 0 || this.type == 4 || this.type == 7) {
        this.isShowSearch = false;
      } else {
        this.isShowSearch = true;
      }
    }

    if (options.isShowWarehouse) {
      this.isShowWarehouse = options.isShowWarehouse
    }

    //接收warehouse和warehouse_name
    console.log('productSelection 处理仓库信息:', {
      warehouse: options.warehouse,
      warehouse_name: options.warehouse_name
    });

    if (options.warehouse !== undefined || options.warehouse_name !== undefined) {
      this.warehouseData.warehouse = options.warehouse ? Number(options.warehouse) : '';
      this.warehouseData.warehouse_name = options.warehouse_name || '';

      console.log('设置后的 warehouseData:', this.warehouseData);
    }

    //接收列表数组
    if (
      options.data !== null &&
      options.data !== "" &&
      options.data !== "{}" &&
      options.data !== "[]"
    ) {
      // 先解码URL编码的数据，再解析JSON
      const decodedData = decodeURIComponent(options.data);
      console.log('解码后的数据:', decodedData);
      const data = JSON.parse(decodedData);
      console.log('解析后的数据:', data);

      // 处理可能包含items和orderDetail的对象
      let itemsData = data;
      if (data && typeof data === 'object' && data.items) {
        itemsData = data.items;
      }

      if (Array.isArray(itemsData) && itemsData.length === 0) return;
      if (
        typeof itemsData === "object" &&
        !Array.isArray(itemsData) &&
        Object.keys(itemsData).length === 0
      )
        return;

      // 计算并添加total_cost字段
      const processedData = itemsData.map((item) => ({
        ...item,
        // 根据不同订单类型使用不同的价格字段
        total_cost: (item.purchase_price || item.price || item.wholesale_price || 0) * (item.quantity || 0),
        // 确保价格字段存在
        purchase_price: item.purchase_price || item.price || item.wholesale_price || 0
      }));
      this.relatedOrderGoods = processedData;

      console.log('处理后的关联订单商品数据:', processedData);
      console.log('relatedOrderGoods 赋值后:', this.relatedOrderGoods);
      console.log('当前 type:', this.type);
    }
  },
  watch: {
  },
  methods: {
    getSearchResult(newVal) {
      this.searchResults = newVal;
    },
    selectedGoodsList(data) {
      console.log(data);

      this.selectGoodsList = data;
    },
    //控制选好了的跳转逻辑
    selectType(type) {
      this.type = type;
    },
    expandShoppingCart() {
      if (this.selectGoodsList.length <= 0 && !this.shoppingCartShow) {
        uni.showToast({
          title: "您需要先挑选商品",
          icon: "none",
        });
        return;
      }
      if (!this.shoppingCartShow) {
        // 展开动画
        this.shoppingCartShow = true;
        this.$nextTick(() => {
          let animation = uni.createAnimation({
            duration: 300,
            timingFunction: "ease",
          });
          animation.translateY(0).opacity(1).step();
          this.cartAnimation = animation.export();
        });
      } else {
        // 收缩动画
        let animation = uni.createAnimation({
          duration: 300,
          timingFunction: "ease",
        });
        animation.translateY("100%").opacity(0).step();
        this.cartAnimation = animation.export();
        setTimeout(() => {
          this.shoppingCartShow = false;
        }, 300);
      }
    },

    //减少数量
    reduceNum(item) {
      console.log(item);
      console.log(this.selectGoodsList);

      item.quantity -= 1;
      
      if (this.type == 6) {//如果是零售单，减少数量，需要变换库存减少量
        const total = 1 * Number(item.conversion_rate)
        const reduceStock = item.remaining_stock - total;

        // 循环selectGoodsList，对所有相同item的商品都减少库存
        this.selectGoodsList.forEach((goods) => {
          if (goods.item === item.item) {
            goods.remaining_stock = reduceStock;
          }
        });
      }

      if (item.quantity <= 0) {
        this.deselectItem = item;
        console.log(item);
        this.selectGoodsList.splice(this.selectGoodsList.indexOf(item), 1);

        // 当商品被移除时，重新计算相同item的remaining_stock
        if (this.type == 6) {
          this.recalculateRemainingStock(item.item);
        }

        if (this.type == 4) {
          uni.removeStorageSync('returnWarehouse');
          eventBus.$emit('returnWarehouse', {});
        }
      }
    },
    // 重新计算相同item的remaining_stock
    recalculateRemainingStock(itemId) {
      // 计算相同item的总用量
      let totalUsedStock = 0;
      this.selectGoodsList.forEach(goods => {
        if (goods.item === itemId) {
          totalUsedStock += Number(goods.quantity || 0) * Number(goods.conversion_rate || 1);
        }
      });

      // 更新所有相同item的remaining_stock
      this.selectGoodsList.forEach(goods => {
        if (goods.item === itemId) {
          goods.remaining_stock = totalUsedStock;
        }
      });
    },
    //增加数量
    addNum(item) {
      item.quantity = Number(item.quantity) + 1;

      if (this.type == 6) {//如果是零售单，增加数量，需要变换库存减少量
        const oldRemainingStock = item.remaining_stock;
        const total = 1 * Number(item.conversion_rate)
        const AddStock = item.remaining_stock + total;

        if (oldRemainingStock == item.total_stock) {
          uni.showToast({
            title: "当前库存不足",
            icon: "none",
            mask: true,
          });
          item.quantity -= 1;
          this.selectGoodsList.forEach((goods) => {
            if (goods.item === item.item) {
              goods.remaining_stock = oldRemainingStock;
            }
          });
          return;
        }
        
        // 更新所有相同item的remaining_stock
        this.selectGoodsList.forEach((goods) => {
          if (goods.item === item.item) {
            goods.remaining_stock = AddStock;
          }
        });
        console.log(item);
      }
    },

    //确认选择商品
    confirmSelectGoods() {
      console.log(this.selectGoodsList);

      eventBus.$emit("selectGoodsList", this.selectGoodsList);
      if (this.type === 0 || this.type === 4 || this.type == 7) {
        uni.navigateBack({
          delta: 2,
        });
      } else {
        uni.navigateBack({
          delta: 1,
        });
      }
    },

    //清空已选商品
    clearSelectGoodsList() {
      if (this.type == 4) {
        uni.removeStorageSync('returnWarehouse');
        eventBus.$emit('returnWarehouse', {});
      }
      this.selectGoodsList.forEach((item) => {
        console.log(item);
        console.log(item.item);

        this.deselectItemList.push(item.item);
      });
      this.selectGoodsList = [];
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .cart-popup-slide-enter-active,
::v-deep .cart-popup-slide-leave-active {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.3s;
}

::v-deep .cart-popup-slide-enter-from,
::v-deep .cart-popup-slide-leave-to {
  transform: translateY(100%);
  opacity: 0;
}

::v-deep .cart-popup-slide-enter-to,
::v-deep .cart-popup-slide-leave-from {
  transform: translateY(0);
  opacity: 1;
}

.cartAndBtn {
  height: 900rpx;
}

.operatingButton {
  width: 90%;
  height: 100rpx;
  background-color: #fff;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
}

.selectGoods {
  width: 50%;
}

.cart_container {
  height: 800rpx;
  background-color: #fff;
}

.categoryConfirmationBtn {
  width: 90%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin: 0 auto;
  padding: 20rpx 0;
}

.divider {
  border: 1px solid #ccc;
  width: 100%;
}

.goods_item {
  width: 90%;
  display: flex;
  align-items: center;
  padding: 10px 0;
  margin: 0 auto;
  border-bottom: 1px solid #f0f0f0;
  background: #fff;
}

.goods_left {
  display: flex;
  align-items: center;
  flex: 1;
}

.goods_img {
  width: 48px;
  height: 48px;
  border-radius: 4px;
  margin-right: 12px;
  object-fit: cover;
}

.goods_info {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.goods_extra {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 28rpx;
  color: #333;

  .stock-tag {
    /* background-color: #fff3cd; */
    border: 1px solid #e88b32;
    border-radius: 12rpx;
    padding: 0rpx 30rpx;
    font-size: 24rpx;
    color: #e88b32;
    white-space: nowrap;
  }
}

.goods_name {
  font-size: 16px;
  font-weight: bold;
  color: #222;
}

.goods_code {
  font-size: 12px;
  color: #888;
  margin: 2px 0;
}

.goods_price {
  display: flex;
  align-items: center;
  margin-top: 2px;
}

.price {
  color: #e22;
  font-size: 14px;
  font-weight: bold;
  margin-right: 2px;
}

.unit {
  color: #e22;
  font-size: 12px;
}

.goods_num {
  display: flex;
  align-items: center;
  margin-left: 10px;
}

.goods_num_input {
  text-align: center;
  font-size: 15px;
  margin: 0 6px;
  color: #222;
}

.cart_mask {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 1000;
}

.cart_popup {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  z-index: 1001;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  max-height: 70vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.08);
}

.cart_container {
  overflow-y: auto;
  max-height: 60vh;
  background-color: #fff;
}

.popup-btn {
  width: 100%;
  position: sticky;
  bottom: 0;
  background: #fff;
  z-index: 10;
  display: flex;
  justify-content: center;
  align-items: center;
}

.operatingButton {
  width: 90%;
  height: 100rpx;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
}

.selectGoods {
  width: 50%;
}

::v-deep .operatingButton .u-button.data-v-3bf2dba7 {
  height: 35px;
  width: 90%;
  position: relative;
  align-items: center;
  justify-content: center;
  display: flex;
  flex-direction: row;
  box-sizing: border-box;
  flex-direction: row;
}
</style>
