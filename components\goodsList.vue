<template>
  <view class="goodsList">
    <scroll-view
      scroll-y
      class="scroll-Y"
      @scrolltolower="loadMore"
      :style="{ height: scrollViewHeight }"
    >
      <view
        class="goods_item"
        v-for="(item, index) in goodsList"
        :key="index"
        @tap="navigateToEdit(item)"
        :class="{ goods_item_disabled: !item.is_active || !item.isWarehouse }"
      >
        <view
          v-if="
            type == 2 ||
            type == 6 ||
            ((item.is_active || item.isWarehouse) && selectGoods) ||
            type == 9
          "
        >
          <view class="goods_theme">
            <view class="goods_img">
              <i-label
                v-if="item.selected"
                class="corner-bg"
                theme="filled"
                size="22"
                fill="#e78615"
              />
              <image
                :src="
                  (item.images_urls && item.images_urls[0] && item.images_urls[0].thumbnail) ||
                  '/static/img/default-goods.png'
                "
                mode=""
              />
            </view>
            <view class="goods_title">
              <view class="goods_name">
                <u-tag
                  text="序"
                  size="mini"
                  :type="
                    !item.is_active || !item.isWarehouse ? 'info' : 'primary'
                  "
                ></u-tag>
                <text
                  style="margin-left: 10rpx"
                  :class="{
                    'text-disabled': !item.is_active || !item.isWarehouse,
                  }"
                  >{{ item.name || item.item_name }}</text
                >
              </view>
              <view
                class="goods_id"
                :class="{
                  'text-disabled': !item.is_active || !item.isWarehouse,
                }"
                >{{ item.code || item.item }}</view
              >
            </view>
            <!-- 添加禁用标识 -->
            <view class="disabled-tag" v-if="!item.is_active">
              <u-icon name="lock-fill" color="#999" size="14"></u-icon>
              <text>已禁用</text>
            </view>
            <!-- 选择商品 -->
            <view
              class="select-goods"
              v-if="selectGoods"
              @click.stop="selectProduct(item)"
            >
              <i-add-one theme="outline" size="20" fill="#509ae8" />
            </view>
          </view>
          <view class="goods_info">
            <!-- 左 -->
            <view class="goods_attributes">
              <view class="goods_attribute">
                <view
                  class="goods_attribute_name"
                  :class="{
                    'text-disabled': !item.is_active || !item.isWarehouse,
                  }"
                  >单位
                </view>
                <view
                  class="goods_attribute_value"
                  :class="{
                    'text-disabled': !item.is_active || !item.isWarehouse,
                  }"
                >
                  ：{{ (item.units && item.units[0] && item.units[0].unit_type_name) || item.unit_name || '个' }}</view
                >
              </view>
              <view
                class="goods_attribute"
                v-if="
                  type == 0 ||
                  type == 3 ||
                  type == 7 ||
                  type == 8 ||
                  type == 4 ||
                  type == 5
                "
              >
                <view
                  class="goods_attribute_name"
                  :class="{ 'text-disabled': !item.is_active }"
                  >单价</view
                >
                <view
                  class="goods_attribute_value"
                  :class="{ 'text-disabled': !item.is_active }"
                  >：{{
                    $toFixed2(
                      item.units && item.units[0]
                        ? item.units[0].price
                        : item.price
                    )
                  }}
                </view>
              </view>
              <view class="goods_attribute" v-if="!selectGoods">
                <view
                  class="goods_attribute_name"
                  :class="{ 'text-disabled': !item.is_active }"
                  >进货价</view
                >
                <view
                  class="goods_attribute_value"
                  :class="{ 'text-disabled': !item.is_active }"
                  >：{{
                    $toFixed2(
                      item.units && item.units[0]
                        ? item.units[0].total_cost
                        : item.total_cost
                    )
                  }}</view
                >
              </view>
              <view class="goods_attribute" v-if="!selectGoods">
                <view
                  class="goods_attribute_name"
                  :class="{ 'text-disabled': !item.is_active }"
                  >批发价</view
                >
                <view
                  class="goods_attribute_value"
                  :class="{ 'text-disabled': !item.is_active }"
                >
                  ：{{
                    $toFixed2(
                      item.units && item.units[0]
                        ? item.units[0].wholesale_price
                        : item.wholesale_price
                    )
                  }}</view
                >
              </view>

              <!-- 退货单字段 -->
              <view
                class="goods_attribute"
                v-if="type == 4 || type == 5 || type == 7 || type == 8"
              >
                <view
                  class="goods_attribute_name"
                  :class="{ 'text-disabled': !item.is_active }"
                  >金额</view
                >
                <view
                  class="goods_attribute_value"
                  :class="{ 'text-disabled': !item.is_active }"
                  >：{{ item.total_cost || item.quantity * item.price }}
                </view>
              </view>
            </view>
            <!-- 右 -->
            <view class="goods_attributes">
              <view class="goods_attribute" v-if="type == 2 || type == 6">
                <view
                  class="goods_attribute_name"
                  :class="{ 'text-disabled': !item.is_active }"
                  >库存</view
                >
                <view
                  class="goods_attribute_value"
                  :class="{ 'text-disabled': !item.is_active }"
                  >：{{ item.total_stock }}
                </view>
              </view>
              <view
                class="goods_attribute"
                v-if="
                  type == 0 || type == 3 || type == 4 || type == 7 || type == 8
                "
              >
                <view
                  class="goods_attribute_name"
                  :class="{ 'text-disabled': !item.is_active }"
                  v-if="type !== 4"
                  >数量
                </view>
                <view
                  class="goods_attribute_name"
                  :class="{ 'text-disabled': !item.is_active }"
                  v-else
                  >原数量</view
                >
                <view
                  class="goods_attribute_value"
                  :class="{ 'text-disabled': !item.is_active }"
                >
                  ：{{ item.quantity }}</view
                >
              </view>
              <view class="goods_attribute" v-if="type == 0 || type == 3">
                <view
                  class="goods_attribute_name"
                  :class="{ 'text-disabled': !item.is_active }"
                  >金额</view
                >
                <view
                  class="goods_attribute_value"
                  :class="{ 'text-disabled': !item.is_active }"
                >
                  ：{{ item.total_cost }}</view
                >
              </view>
              <view class="goods_attribute" v-if="!selectGoods">
                <view
                  class="goods_attribute_name"
                  :class="{ 'text-disabled': !item.is_active }"
                  >零售价</view
                >
                <view
                  class="goods_attribute_value"
                  :class="{ 'text-disabled': !item.is_active }"
                >
                  ：{{
                    $toFixed2(
                      item.units && item.units[0]
                        ? item.units[0].retail_price
                        : item.retail_price
                    )
                  }}
                </view>
              </view>
              <view class="goods_attribute" v-if="!selectGoods">
                <view
                  class="goods_attribute_name"
                  :class="{ 'text-disabled': !item.is_active }"
                  >最低售价</view
                >
                <view
                  class="goods_attribute_value"
                  :class="{ 'text-disabled': !item.is_active }"
                >
                  ：{{
                    $toFixed2(
                      item.units && item.units[0]
                        ? item.units[0].min_price
                        : item.min_price
                    )
                  }}</view
                >
              </view>

              <!-- 退货单字段 -->
              <view class="goods_attribute" v-if="type == 4 || type == 5">
                <view
                  class="goods_attribute_name"
                  :class="{ 'text-disabled': !item.is_active }"
                  >仓库</view
                >
                <view
                  class="goods_attribute_value"
                  :class="{ 'text-disabled': !item.is_active }"
                >
                  ：{{ item.warehouse_name }}</view
                >
              </view>
              <view class="goods_attribute" v-if="type == 4 || type == 5">
                <view
                  class="goods_attribute_name"
                  :class="{ 'text-disabled': !item.is_active }"
                  >库存余量</view
                >
                <view
                  class="goods_attribute_value"
                  :class="{ 'text-disabled': !item.is_active }"
                >
                  ：{{ item.remaining_quantity }}</view
                >
                <view
                  style="margin-left: 10rpx"
                  @click.stop="record(item)"
                  v-if="item.remaining_quantity !== item.quantity"
                >
                  <i-attention theme="outline" size="16" fill="#d13b3b" />
                </view>
              </view>

              <!-- 零售退货单字段 -->
              <view class="goods_attribute" v-if="type == 7 || type == 8">
                <view
                  class="goods_attribute_name"
                  :class="{ 'text-disabled': !item.is_active }"
                  >已退货</view
                >
                <view
                  class="goods_attribute_value"
                  :class="{ 'text-disabled': !item.is_active }"
                >
                  ：{{ item.remaining_quantity }}</view
                >
                <view
                  style="margin-left: 10rpx"
                  @click.stop="record(item)"
                  v-if="item.remaining_quantity !== item.quantity"
                >
                  <i-attention theme="outline" size="16" fill="#d13b3b" />
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
      <!-- 加载更多 -->
      <view class="loading-more" v-if="loading">
        <u-loading-icon></u-loading-icon>
        <text>加载中...</text>
      </view>
      <view class="no-more" v-if="!hasMore && goodsList.length > 0">
        <text>没有更多数据了</text>
      </view>
      <view class="empty" v-if="!loading && goodsList.length === 0">
        <text>暂无数据</text>
      </view>
    </scroll-view>

    <productDetails
      :productDetailsShow="productDetailsShow"
      :productData="productData"
      :isShowDelBtn="false"
      :type="type"
      :isShowUnit="isShowUnit"
      :selectedGoodsQuantity="selectedGoodsQuantity"
      @close="closeSelectPopup"
      @confirm="confirmSelectProduct"
    />

    <!-- 销货记录框 -->
    <view>
      <u-popup
        :show="isShowPayAttentionTo"
        mode="bottom"
        @close="close"
        @open="handlePayAttentionTo"
      >
        <!-- 标题栏 -->
        <view class="popup-title-container">
          <view class="popup-title">
            <text style="font-size: 32rpx; font-weight: bold">出库记录</text>
          </view>
          <view style="position: absolute; right: 30rpx" @click.stop="close">
            <i-close-one theme="outline" size="24" fill="#df7777" />
          </view>
        </view>
        <!-- 出入库记录列表 -->
        <view class="sales-list">
          <view v-for="(item, index) in goodsExitRecord" :key="index">
            <view class="sales-item">
              <text>单号：{{ item.history_order_id }}</text>
              <text
                >{{ getHistoryTypeText(item.history_type) }}：{{
                  item.quantity
                }}</text
              >
              <text>日期：{{ item.history_date }}</text>
            </view>
          </view>
          <view class="cumulative">
            累计出货：{{ cumulativeReturnGoods }}
          </view>
        </view>
      </u-popup>
    </view>
  </view>
</template>

<script>
import productDetails from "@/components/productDetails.vue";
import { getGoods, getGoodsDetail, getGoodsPages } from "@/api/goods";
import { entranceAndExitRecord } from "@/api/inventory";
import eventBus from "@/utils/eventBus";

export default {
  beforeDestroy() {
    // 移除事件监听器
    eventBus.$off('relatedOrderItemsSelected');
  },
  components: {
    productDetails,
  },
  options: {
    styleIsolation: "shared",
  },
  props: {
    isGoodsManageRefresh: {
      //是否刷新
      type: Boolean,
      default: false,
    },
    searchResults: {
      //搜索结果
      type: Array,
      default: () => [],
    },
    selectGoods: {
      //是否作为选择商品的页面
      type: Boolean,
      default: false,
    },
    deselectItem: {
      //取消选择商品
      type: Object,
      default: () => {},
    },
    deselectItemList: {
      //取消选择商品列表
      type: Array,
      default: () => [],
    },
    relatedOrderGoods: {
      //关联订单商品
      type: Array,
      default: () => [],
    },
    type: {
      /*
        0：进货关联订单进入
        1：任意商品
        2：采购订单/物品管理
        3：进货订单点击添加关联物品
        4: 退货单:进货关联单进入
        5: 批发订单关联销售订单进入/退货单:任意商品进入
        6: 零售订单
        7: 零售退货订单:零售关联单进入
        8: 零售退货订单:任意商品进入
        9: 物品管理
       */
      type: Number,
      default: 0,
    },
    includeInactive: {
      // 是否包含禁用物品
      type: Boolean,
      default: false,
    },
    warehouseData: {
      // 仓库数据
      type: Object,
      default: () => ({
        warehouse: '',
        warehouse_name: ''
      }),
    },
  },
  data() {
    return {
      goodsListParams: {
        page: 1,
        page_size: 10,
        include_inactive: this.includeInactive,
        warehouse_id: "",
      },
      pageData: getGoodsPages(
        {
          include_inactive: this.includeInactive,
          warehouse_id: "",
        },
        {
          fakeLoadCallback: this.fakeLoad,
          pageOneCallback: this.pageOne,
          expandCallback: this.expandPage,
        }
      ),
      goodsList: [], //商品列表
      loading: false,
      hasMore: true,
      scrollViewHeight: "500px",
      selectGoodsList: [], //被选中的商品
      productDetailsShow: "", //是否展示商品详情弹出层
      productData: {
        purchase_order_item: "", //关联订单的id
      }, //商品详情数据
      returnWarehouse: {
        warehouse: "",
        warehouse_name: "",
      }, //退货时选择的仓库
      goodsExitRecord: [], //商品出库记录
      cumulativeReturnGoods: 0, //商品出库记录
      isShowPayAttentionTo: false, //销货记录框
      selectedGoodsQuantity: [], // [{id: 商品id, quantity: 总数量}]
      isShowUnit: true, //在详细信息弹出层是否显示单位选择器
    };
  },
  watch: {
    includeInactive: {
      handler(newVal) {
        // 当 includeInactive 属性变化时，更新查询参数并重新加载数据
        this.goodsListParams.include_inactive = newVal;
        this.pageData.extra_param.include_inactive = newVal;
        if (this.type === 1 || this.type === 2 || this.type === 6 || this.type === 5) {
          // 重置分页并重新加载数据
          this.goodsListParams.page = 1;
          this.goodsList = [];
          this.hasMore = true;
          this.getGoodsList();
        }
      },
      immediate: true,
    },
    warehouseData: {
      handler(newVal) {
        // 当仓库数据变化时，更新查询参数并重新加载数据
        if (newVal && newVal.warehouse) {
          console.log('仓库数据变化，重新加载商品列表:', newVal.warehouse);
          this.goodsListParams.warehouse_id = newVal.warehouse;
          this.pageData.extra_param.warehouse_id = newVal.warehouse;

          if (this.type === 1 || this.type === 2 || this.type === 6 || this.type === 5) {
            // 重置分页并重新加载数据
            this.goodsListParams.page = 1;
            this.goodsList = [];
            this.hasMore = true;
            this.getGoodsList();
          }
        }
      },
      deep: true,
      immediate: false
    },
    searchResults: {
      handler(newVal) {
        console.log(newVal);
        if (
          this.relatedOrderGoods.length > 0 ||
          (this.type !== 1 && this.type !== 2 && this.type !== 6)
        ) {
          return;
        }
        if (newVal.length > 0) {
          // 过滤掉 relatedOrderGoods 中已存在的项
          const filteredItems = newVal.filter(
            (item) =>
              !this.relatedOrderGoods?.some(
                (relatedItem) => relatedItem.item === item.id
              )
          );
          this.goodsList = this.mapGoodsItems(filteredItems);
        } else {
          this.goodsListParams.page = 1;
          this.getGoodsList();
        }
      },
      // 移除 immediate: true
    },
    relatedOrderGoods: {
      handler(newVal) {
        console.log(newVal);

        if (newVal && newVal.length > 0) {
          this.goodsList = this.mapGoodsItems(newVal);
        }

        if (this.type == 4 || this.type == 5) {
          this.filterItemsForWarehouse();
        }
      },
      immediate: true,
    },
    deselectItem: {
      handler(newVal) {
        console.log(newVal);
        console.log(this.selectGoodsList);

        this.goodsList.forEach((g) => {
          if (g.item === newVal.item) {
            // 检查selectGoodsList中是否还有相同item的商品
            const hasOtherSameItem = this.selectGoodsList.some(
              (selectedItem) =>
                selectedItem.item === newVal.item &&
                !(
                  selectedItem.item === newVal.item &&
                  selectedItem.unit === newVal.unit
                )
            );

            // 只有当selectGoodsList中没有其他相同item的商品时，才将selected设为false
            if (!hasOtherSameItem) {
              g.selected = false;
            }
          }
        });
      },
      immediate: true,
    },
    deselectItemList: {
      handler(newVal) {
        console.log(newVal);
        this.selectGoodsList = [];
        this.goodsList.forEach((g) => {
          if (newVal.includes(g.item)) {
            // 检查selectGoodsList中是否还有相同item的商品
            const hasOtherSameItem = this.selectGoodsList.some(
              (selectedItem) => selectedItem.item === g.item
            );

            // g.remaining_stock = hasOtherSameItem ? g.remaining_stock : 0;
            // 只有当selectGoodsList中没有其他相同item的商品时，才将selected设为false
            if (!hasOtherSameItem) {
              g.selected = false;
            }
          }
        });
      },
      immediate: true,
    },
    isGoodsManageRefresh: {
      handler(newVal) {
        console.log(newVal);

        if (newVal) {
          if (
            this.type !== 0 &&
            this.type !== 3 &&
            this.type !== 4 &&
            this.type !== 5
          ) {
            this.getGoodsList();

            //重置刷新状态
            this.$emit("update:isGoodsManageRefresh", false);
          }
        }
      },
      // immediate: true,
    },
    returnWarehouse: {
      handler(newVal) {
        console.log(newVal);

        this.filterItemsForWarehouse();
      },
      immediate: true,
    },
  },
  created() {
    eventBus.$on("returnWarehouse", (data) => {
      if (Object.keys(data).length > 0) {
        this.returnWarehouse = data;
      } else {
        this.returnWarehouse = {
          warehouse: "",
          warehouse_name: "",
        };
        this.goodsList.forEach((e) => {
          e.isWarehouse = true;
        });
      }
    });
    this.filterItemsForWarehouse();
  },
  onShow() {
    this.filterItemsForWarehouse();
  },
  mounted() {
    console.log(this.type);

    // 监听 relatedOrderItemsSelected 事件
    eventBus.$on('relatedOrderItemsSelected', (data) => {
      console.log('接收到 relatedOrderItemsSelected 事件数据:', data);

      // 处理不同格式的数据
      let itemsData = data;
      if (data && typeof data === 'object' && data.items) {
        // 如果数据包含 items 属性，使用 items 数组
        itemsData = data.items;
      }

      if (itemsData && Array.isArray(itemsData) && itemsData.length > 0) {
        this.relatedOrderGoods = itemsData;
        console.log('goodsList 组件接收到关联订单商品:', itemsData);
      }
    });

    this.initScrollViewHeight();
    // this.pageData.fakeLoadCallback = this.fakeLoad;
    // this.pageData.pageOneCallback = this.pageOne;
    // this.pageData.expandCallback = this.expandPage;
    //如果是进货单关联订单进入，商品详细层不展示单位选择器
    if (this.type == 0 || this.type == 3 || this.type == 7 || this.type == 8) {
      this.isShowUnit = false;
    }
    //如果是零售单进入，需要传入仓库id
    if (this.type == 6) {
      this.goodsListParams.warehouse_id =
        uni.getStorageSync("retailWarehouseId");
      this.pageData.extra_param.warehouse_id =
        uni.getStorageSync("retailWarehouseId");
    }

    // 如果传入了仓库数据，设置仓库ID参数（适用于批发订单等场景）
    if (this.warehouseData && this.warehouseData.warehouse) {
      console.log('设置仓库ID参数:', this.warehouseData.warehouse);
      this.goodsListParams.warehouse_id = this.warehouseData.warehouse;
      this.pageData.extra_param.warehouse_id = this.warehouseData.warehouse;
    }

    //如果是进货单、采购单、零售单进入，需要请求数据
    //批发订单关联(type=5)不需要请求，使用传入的relatedOrderGoods数据
    if (this.type == 1 || this.type == 2 || this.type == 6) {
      this.getGoodsList();
    }
  },
  methods: {
    // 销货记录框关闭弹窗
    close() {
      this.isShowPayAttentionTo = false;
    },
    //打开销货记录
    handlePayAttentionTo(item) {
      this.isShowPayAttentionTo = true;
    },
    // 获取历史记录类型文本
    getHistoryTypeText(type) {
      const typeMap = {
        pi: "采购入库",
        so: "销售出库",
        ai: "调拨入库",
        ao: "调拨出库",
        ami: "组装入库",
        amo: "组装出库",
        rpi: "维修入库",
        rpo: "维修出库",
        rin: "退货入库",
        rot: "退货出库",
        srt: "库存退货",
        ini: "初始入库",
      };
      return typeMap[type] || type;
    },

    //出入库记录
    record(item) {
      console.log(item);

      this.cumulativeReturnGoods = 0;
      entranceAndExitRecord({ stock_id: item.id })
        .then((res) => {
          console.log(res);
          if (res.code == 0) {
            // 修复：使用 res.data.results 而不是 res.data
            this.goodsExitRecord = res.data.results || res.data;
            this.cumulativeReturnGoods = 0;
            this.goodsExitRecord.forEach((e) => {
              // 计算所有出库记录的累计数量（负数表示出库）
              if (parseFloat(e.quantity) < 0) {
                this.cumulativeReturnGoods += Math.abs(parseFloat(e.quantity));
              }
            });
            this.isShowPayAttentionTo = true;
          } else {
            uni.showToast({
              title: res.msg,
              icon: "none",
            });
          }
        })
        .catch((err) => {
          console.error("获取出入库记录失败:", err);
          uni.showToast({
            title: "获取记录失败",
            icon: "none",
          });
        });
    },

    filterItemsForWarehouse() {
      const data = uni.getStorageSync("returnWarehouse");
      if (data) {
        console.log(data);

        this.returnWarehouse.warehouse = data.warehouse;
        this.returnWarehouse.warehouse_name = data.warehouse_name;
        const matchedItems = [];
        const unmatchedItems = [];
        this.goodsList.forEach((item) => {
          if (
            item.warehouse &&
            item.warehouse !== this.returnWarehouse.warehouse
          ) {
            item.isWarehouse = false;
            unmatchedItems.push(item);
          } else {
            matchedItems.push(item);
          }
        });
        this.goodsList = [...matchedItems, ...unmatchedItems];
        console.log(this.goodsList);
      }
    },

    mapGoodsItems(items) {
      return items.map((item) => {
        // 基础映射
        const mappedItem = {
          ...item,
          selected: false,
          isWarehouse: true,
          ...(items === this.relatedOrderGoods ? { is_active: true } : {}),
        };

        // 如果是销售订单详情数据（type=5），进行字段适配
        if (this.type === 5 && items === this.relatedOrderGoods) {
          // 确保必要字段存在
          mappedItem.name = mappedItem.name || mappedItem.item_name || '未知商品';
          mappedItem.code = mappedItem.code || mappedItem.item || '未知编码';
          mappedItem.unit_name = mappedItem.unit_name || '个';
          mappedItem.price = mappedItem.price || mappedItem.wholesale_price || 0;
          mappedItem.quantity = mappedItem.quantity || mappedItem.wholesale_quantity || 0;
          mappedItem.stock_quantity = mappedItem.stock_quantity || 0;

          // 确保 units 数组结构存在
          if (!mappedItem.units || !Array.isArray(mappedItem.units)) {
            mappedItem.units = [{
              id: mappedItem.unit || 1,
              unit_type_name: mappedItem.unit_name || '个',
              unit_name: mappedItem.unit_name || '个'
            }];
          }

          // 确保 images_urls 数组结构存在
          if (!mappedItem.images_urls || !Array.isArray(mappedItem.images_urls)) {
            mappedItem.images_urls = [{
              thumbnail: '/static/img/default-goods.png',
              url: '/static/img/default-goods.png'
            }];
          }

          // 确保第一个图片对象存在
          if (mappedItem.images_urls.length === 0) {
            mappedItem.images_urls.push({
              thumbnail: '/static/img/default-goods.png',
              url: '/static/img/default-goods.png'
            });
          }

          // 确保第一个图片对象有 thumbnail 属性
          if (!mappedItem.images_urls[0].thumbnail) {
            mappedItem.images_urls[0].thumbnail = '/static/img/default-goods.png';
          }

          // 计算金额（如果没有amount字段）
          if (!mappedItem.amount && !mappedItem.total_cost) {
            mappedItem.amount = parseFloat(mappedItem.quantity || 0) * parseFloat(mappedItem.price || 0);
            mappedItem.total_cost = mappedItem.amount;
          }

          console.log('适配后的销售订单商品数据:', mappedItem);
        }

        // 为所有商品确保基本的图片结构（防止其他类型也出现问题）
        if (!mappedItem.images_urls || !Array.isArray(mappedItem.images_urls) || mappedItem.images_urls.length === 0) {
          mappedItem.images_urls = [{
            thumbnail: '/static/img/default-goods.png',
            url: '/static/img/default-goods.png'
          }];
        }

        // 确保第一个图片对象有 thumbnail 属性
        if (mappedItem.images_urls[0] && !mappedItem.images_urls[0].thumbnail) {
          mappedItem.images_urls[0].thumbnail = '/static/img/default-goods.png';
        }

        return mappedItem;
      });
    },
    initScrollViewHeight() {
      try {
        const info = uni.getSystemInfoSync();
        const screenWidth = info.screenWidth;

        const navBarHeight = 44; // 导航栏高度（标准高度）
        const statusBarHeight = info.statusBarHeight; // 状态栏高度
        const searchHeight = (screenWidth * Number.parseInt(80)) / 750; // 搜索框高度 80rpx
        const inputHeight = (screenWidth * Number.parseInt(90)) / 750; // 底部输入区域高度 90rpx
        const selectBtnHeight = (screenWidth * Number.parseInt(100)) / 750; // 底部输入区域高度 100rpx
        let totalHeight = 0;
        if (
          this.type == 0 ||
          this.type == 4 ||
          this.type == 5 ||
          this.type == 7 ||
          this.type == 8
        ) {
          totalHeight = navBarHeight + inputHeight + selectBtnHeight;
        } else if (this.selectGoods) {
          totalHeight =
            navBarHeight + searchHeight + inputHeight + selectBtnHeight;
        } else {
          totalHeight = navBarHeight + searchHeight + inputHeight;
        }
        console.log(totalHeight);

        // 计算最终的滚动区域高度
        const scrollHeight = info.windowHeight - totalHeight;
        this.scrollViewHeight = `${scrollHeight}px`;
      } catch (e) {
        console.error("获取系统信息失败：", e);
      }
    },
    //获取商品信息
    getGoodsList(isLoadMore = false) {
      // 只要有 relatedOrderGoods 或 searchResults，就不请求
      if (this.relatedOrderGoods.length > 0 && this.type == 0) {
        return;
      }
      if (isLoadMore) {
        this.pageData.nextPage();
      } else {
        this.pageData.firstPage();
      }
      this.loading = true;
      // getGoods(this.goodsListParams)
      //   .then((res) => {
      //     // 统一过滤逻辑：排除已在关联订单中的商品的单位
      //     // if (this.relatedOrderGoods.length > 0) {
      //     //   this.goodsList = [];
      //     //   this.relatedOrderGoods.forEach(related => {
      //     //     const match = res.data.results.find(item => item.id === related.item);
      //     //     if (match && match.units) {
      //     //       match.units = match.units.filter(unit => unit.id !== related.unit);
      //     //       res.data.results.push(match);
      //     //     }
      //     //   });
      //     // }
      //     if (isLoadMore) {
      //       this.goodsList = [...this.goodsList, ...res.data.results];
      //     } else {
      //       this.goodsList = res.data.results;
      //     }
      //     // 统一处理选中状态
      //     this.goodsList = this.mapGoodsItems(this.goodsList);
      //     console.log(this.goodsList);
      //     if (res.code != 0) {
      //       uni.showToast({
      //         title: res.msg,
      //         icon: "none",
      //       });

      //     }
      //     //判断是否有更多数据
      //     if (this.goodsList.length >= res.data.count) {
      //       this.hasMore = false;
      //     }
      //   })
      //   .catch((err) => {
      //     console.error("获取商品列表失败：", err);
      //     uni.showToast({
      //       title: res.msg,
      //       icon: "none",
      //     });
      //   })
      //   .finally(() => {
      //     this.loading = false;
      //   });
    },
    //触底加载
    loadMore() {
      if (
        (this.relatedOrderGoods && this.type == 0) ||
        this.relatedesults ||
        !this.hasMore ||
        this.loading
      )
        return;

      console.log('触发加载更多，当前页码:', this.goodsListParams.page);
      this.goodsListParams.page += 1;
      this.getGoodsList(true);
    },
    //跳转编辑商品
    navigateToEdit(item) {
      if (this.selectGoods) {
        this.selectProduct(item);
        return;
      }
      getGoodsDetail(item.id).then((res) => {
        if (res.code == 0) {
          uni.navigateTo({
            url:
              "/pages/commodityManagement/editProduct/editProduct?item=" +
              encodeURIComponent(JSON.stringify(res.data)),
          });
        }
      });
    },

    //选择商品
    selectProduct(item) {
      console.log(item);
      console.log(this.selectGoodsList);
      this.selectGoodsList.forEach((selectedItem) => {
        // 将selectGoodsList中的remaining_stock同步到goodsList中相同item的元素
        this.goodsList.forEach((goodsItem) => {
          if (goodsItem.item === selectedItem.item) {
            goodsItem.remaining_stock = selectedItem.remaining_stock;
          }
        });
      });
      this.productData = item;
      console.log(this.productData);
      if (!item.isWarehouse) {
        uni.showToast({
          title: "此商品所在仓库与当前仓库不一致",
          icon: "none",
        });
        return;
      }

      if (this.type === 4 || this.type === 5) {
        this.returnWarehouse = this.returnWarehouse || {};
      }

      let id = 0;
      if ((this.productData.id && this.type == 0) || this.type == 3) {
        this.productData.purchase_order_item = this.productData.id;
        id = item.item;
        // item.item = item.id
      }
      if (this.type === 1 || this.type == 2 || this.type == 6) {
        item.item = item.id;
        id = item.id;
      }
      if (this.type == 4 || this.type == 5) {
        id = item.item;
        this.returnWarehouse.warehouse = item.warehouse;
        this.returnWarehouse.warehouse_name = item.warehouse_name;
      }
      //退货零售单时
      if (this.type == 7 || this.type == 8) {
        id = item.item;
      }

      // const targetProduct = this.selectGoodsList.find(
      //   (items) => items.id === item.id
      // );
      // if (targetProduct) {
      //   this.productData = targetProduct;
      //   this.productDetailsShow = true;
      // } else {
      getGoodsDetail(id).then((res) => {
        if (res.code == 0) {
          this.productData = { ...item, ...res.data };
          this.productData.id = item.id;
          this.productData.unit = item.unit;
          console.log(this.productData);

          this.productDetailsShow = true;
        }
      });
      // }
    },

    fakeLoad(data) {
      console.log("fakeLoad", data);
      this.goodsList = data.results;
      this.goodsList = this.mapGoodsItems(this.goodsList);
      this.hasMore = data.count > data.results.length;
      this.loading = false;
    },
    pageOne(data) {
      console.log("pageOne", data);
      const result = this.mapGoodsItems(data.results);

      // 合并逻辑：如果是相同元素就覆盖，不同元素才加在后面
      result.forEach((newItem) => {
        const existingIndex = this.goodsList.findIndex(
          (existingItem) =>
            existingItem.id === newItem.id && existingItem.unit === newItem.unit
        );

        if (existingIndex !== -1) {
          // 找到相同元素，覆盖现有元素
          this.$set(this.goodsList, existingIndex, newItem);
        } else {
          // 不同元素，添加到列表后面
          this.goodsList.push(newItem);
        }
      });

      this.hasMore = data.count > this.goodsList.length;
      this.loading = false;

      console.log(this.goodsList);
    },
    expandPage(data, page) {
      console.log("expandPage", data, page);

      // 检查是否是最后一页的标识
      if (data.isLastPage || (data.results && data.results.length === 0)) {
        console.log("已到达最后一页，没有更多数据");
        this.hasMore = false;
        this.loading = false;
        return;
      }

      const result = this.mapGoodsItems(data.results);

      // 合并逻辑：如果是相同元素就覆盖，不同元素才加在后面
      result.forEach((newItem) => {
        const existingIndex = this.goodsList.findIndex(
          existingItem => existingItem.id === newItem.id && existingItem.unit === newItem.unit
        );

        if (existingIndex !== -1) {
          // 找到相同元素，覆盖现有元素
          this.$set(this.goodsList, existingIndex, newItem);
        } else {
          // 不同元素，添加到列表后面
          this.goodsList.push(newItem);
        }
      });

      this.hasMore = data.count > this.goodsList.length;
      this.loading = false;
    },


    //用户取消选择此商品
    closeSelectPopup(item) {
      this.productDetailsShow = false;
    },
    //用户提交选择此商品
    confirmSelectProduct(item) {
      console.log(item);
      item.id = "";
      // 查找是否已存在相同商品和相同单位的项目
      // 使用商品ID和单位ID作为唯一标识，确保只有相同物品相同单位才会覆盖
      const index = this.selectGoodsList.findIndex(
        (g) => g.item === item.item && g.unit === item.unit
      );

      if (index > -1) {
        // 存在则覆盖原元素
        this.selectGoodsList.splice(index, 1, {
          ...this.selectGoodsList[index],
          ...item,
          selected: true,
        });
      } else {
        // 不存在则添加新元素
        this.selectGoodsList.push({ ...item, selected: true });
      }
      console.log(this.selectGoodsList);
      //变换添加/编辑图标
      const indexGoods = this.goodsList.findIndex((g) => {
        if (this.type === 0 || this.type === 3) {
          return g.item === item.item && g.unit === item.unit;
        }
        return g.item === item.item;
      });
      console.log(indexGoods);

      if (indexGoods > -1) {
        this.$set(this.goodsList, indexGoods, {
          ...this.goodsList[indexGoods],
          selected: true,
        });
      }
      this.productDetailsShow = false;

      // 只有当type为6时才计算selectedGoodsQuantity
      if (this.type === 6) {
        // // 创建一个对象来累计相同id的数量
        // const quantityMap = {};

        // this.selectGoodsList.forEach(items => {
        //   console.log(items);

        //   const itemId = items.item || items.item;
        //   const quantity = Number(items.quantity || 0) * Number(items.conversion_rate || 1);
        //   console.log("变化率过后的数量" + quantity);

        //   if (quantityMap[itemId]) {
        //     quantityMap[itemId] += quantity;
        //   } else {
        //     quantityMap[itemId] = quantity;
        //   }
        // });

        // 将累计结果转换为数组格式
        // this.selectedGoodsQuantity = Object.keys(quantityMap).map(item => ({
        //   id: Number(item),
        //   quantity: quantityMap[item]
        // }));

        // console.log('更新后的selectedGoodsQuantity:', this.selectedGoodsQuantity);
        // // 循环匹配相同id元素，更新remaining_stock
        // this.selectedGoodsQuantity.forEach(selectedItem => {
        //   this.selectGoodsList.forEach(item => {
        //     if (selectedItem.id === item.item) {
        //       item.remaining_stock = selectedItem.quantity;
        //     }
        //   });
        // });


        // 初始化一个对象存储每个item的总量
        const totalMap = {};

        // 第一次循环，计算每个item的总量
        this.selectGoodsList.forEach((item) => {
          const itemId = item.item;
          const quantity =
            Number(item.quantity || 0) * Number(item.conversion_rate || 1);
          if (totalMap[itemId]) {
            totalMap[itemId] += quantity;
          } else {
            totalMap[itemId] = quantity;
          }
        });

        // 第二次循环，更新每个item的remaining_stock
        this.selectGoodsList.forEach((item) => {
          item.remaining_stock = totalMap[item.item] || 0;
        });
        this.goodsList.forEach((item) => {
          item.remaining_stock = totalMap[item.item] || 0;
        });
        console.log(this.selectGoodsList);

        // 更新selectedGoodsQuantity
        // this.selectedGoodsQuantity = Object.keys(totalMap).map(itemId => ({
        //   id: Number(itemId),
        //   quantity: totalMap[itemId]
        // }));

        // console.log('更新后的selectedGoodsQuantity:', this.selectedGoodsQuantity);
        // 循环匹配相同id元素，更新remaining_stock
        // this.selectedGoodsQuantity.forEach(selectedItem => {
        //   this.selectGoodsList.forEach(item => {
        //     if (selectedItem.id === item.item) {
        //       item.remaining_stock = selectedItem.quantity;
        //     }
        //   });
        // });
      }

      this.$emit("selectedGoodsList", this.selectGoodsList);
      console.log(this.selectGoodsList);

      this.$emit("selectType", this.type);
    },
  },
};
</script>

<style lang="scss" scoped>
.goods_item_disabled {
  opacity: 0.7;
  background: #f5f5f5;
}

.text-disabled {
  color: #999 !important;
}

.disabled-tag {
  position: absolute;
  right: 10rpx;
  top: 10rpx;
  display: flex;
  align-items: center;
  padding: 4rpx 10rpx;
  background: #f0f0f0;
  border-radius: 4rpx;

  text {
    font-size: 24rpx;
    color: #999;
    margin-left: 4rpx;
  }
}

.goods_theme {
  position: relative;
}

.corner-bg {
  position: absolute;
  top: -10px;
  left: -14px;
}

.goodsList {
  width: 100%;
  height: 100%;
  background-color: #f5f5f5;

  .scroll-Y {
    width: 100%;
  }

  .goods_item {
    width: 90%;
    background-color: #fff;
    margin: 10rpx auto;
    padding: 20rpx;
    cursor: pointer;
    position: relative;

    .goods_theme {
      width: 100%;
      // height: 40%;
      position: relative;
      display: flex;
      align-items: center;
      flex-direction: row;

      .select-goods {
        position: absolute;
        right: 0;

        top: 0;
      }

      .goods_img {
        width: 100rpx;
        height: 100rpx;
        margin: 10rpx;

        image {
          width: 100%;
          height: 100%;
        }
      }

      .goods_title {
        width: 70%;
        height: 100%;
        display: flex;
        // align-items: center;
        // justify-content: center;
        flex-direction: column;

        .goods_name {
          font-size: 30rpx;
          font-weight: bold;
          display: flex;
          align-items: center;

          ::v-deep .u-tag--primary {
            background-color: #62c8ee;
            border-width: 1px;
            border-color: #62c8ee;
          }

          ::v-deep .u-tag--mini {
            height: 18px;
            /* line-height: 22px; */
            padding: 0 5px;
          }

          .goods_id {
            font-size: 24rpx;
            font-weight: bold;
          }
        }
      }
    }

    .goods_info {
      display: flex;

      .goods_attributes {
        width: 50%;
        font-size: 24rpx;
        display: flex;
        align-content: flex-start;
        align-items: flex-start;
        flex-direction: column;

        .goods_attribute {
          display: flex;
          align-items: center;
          margin-top: 10rpx;
        }
      }
    }
  }

  .loading-more,
  .no-more,
  .empty {
    text-align: center;
    padding: 20rpx;
    color: #999;
    font-size: 24rpx;
    display: flex;
    align-items: center;
    justify-content: center;

    text {
      margin-left: 10rpx;
    }
  }
}

.goods_attribute_name {
  width: 60px;
  display: inline-block;
  text-align-last: justify;
  text-align: justify;
  text-justify: distribute-all-lines;
}

.popup-title-container {
  display: flex;
  align-items: center;
  padding: 20rpx;
  border-bottom: 1px solid #eee;
  position: relative;
}

.popup-title {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.sales-item {
  width: 95%;
  display: flex;
  flex-direction: column;
  padding: 20rpx;
  margin: 0 auto;
  border-bottom: 1px solid #eee;

  text {
    margin-bottom: 8rpx;
    font-size: 28rpx;
  }

  text:first-child {
    font-weight: bold;
    color: #333;
  }

  text:nth-child(2) {
    color: #666;
  }

  text:last-child {
    color: #999;
    font-size: 24rpx;
  }
}

.cumulative {
  height: 50rpx;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: red;
  font-size: 28rpx;
}
</style>
