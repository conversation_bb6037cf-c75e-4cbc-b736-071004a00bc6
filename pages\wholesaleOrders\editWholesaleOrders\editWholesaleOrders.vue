<template>
  <view class="container">
    <scroll-view scroll-y class="content">
      <!-- 基本信息 -->
      <view class="info">
        <view class="info_title">
          <text>基本信息</text>
        </view>
        <view class="form">
          <u--form :model="retailOrderInfo" :rules="rules" ref="uForm" labelPosition="left">
            <u-form-item :label-width="'180rpx'" label="客户" prop="customer_name" borderBottom required>
              <u--input v-model="retailOrderInfo.customer_name" border="none" placeholder="请选择客户" inputAlign="right"
                disabled disabledColor="#fff" @tap="openSelector(1)">
              </u--input>
            </u-form-item>
            <u-form-item :label-width="'180rpx'" label="单据日期" borderBottom>
              <u--input v-model="retailOrderInfo.out_date" border="none" placeholder="请选择日期" inputAlign="right" disabled
                disabledColor="#fff"></u--input>
            </u-form-item>
            <u-form-item :label-width="'180rpx'" label="单据编号" borderBottom>
              <u--input v-model="retailOrderInfo.order_no" border="none" placeholder="提交后自动生成" inputAlign="right"
                disabled disabledColor="#fff"></u--input>
            </u-form-item>
            <u-form-item :label-width="'180rpx'" label="关联订单" borderBottom>
              <u-row>
                <u-col :span="retailOrderInfo.related_order_code ? 10.5 : 12">
                  <u--input v-model="retailOrderInfo.related_order_code" border="none" placeholder="请选择关联订单"
                    inputAlign="right" disabled disabledColor="#fff" @tap="openRelatedOrder()"></u--input>
                </u-col>
                <u-col span="0.5"></u-col>
                <u-col span="1">
                  <view @click="removeRelatedOrder">
                    <i-close-one theme="filled" size="20" fill="#d13b3b" v-if="retailOrderInfo.related_order_code" />
                  </view>
                </u-col>
              </u-row>
            </u-form-item>
            <u-form-item :label-width="'180rpx'" label="仓库" prop="warehouse_name" borderBottom required>
              <u-row>
                <u-col :span="retailOrderInfo.warehouse_name ? 10.5 : 12">
                  <u--input v-model="retailOrderInfo.warehouse_name" border="none" placeholder="请选择仓库"
                    inputAlign="right" disabled disabledColor="#fff" @tap="openInventorySelection()"></u--input>
                </u-col>
                <u-col span="0.5"></u-col>
                <u-col span="1">
                  <view @click="removeWarehouseName">
                    <i-close-one theme="filled" size="20" fill="#d13b3b" v-if="retailOrderInfo.warehouse_name" />
                  </view>
                </u-col>
              </u-row>
            </u-form-item>
          </u--form>
        </view>
      </view>

      <!-- 商品清单 -->
      <merch-bill
        :items="retailOrderInfo.items"
        type="retail"
        :is-view="isView"
        @add-goods="addGoods"
        @open-product-details="openProductDetails"
        @amount-change="handleAmountChange" />

      <!-- 结算信息 -->
      <view class="info">
        <view class="info_title">
          <text>结算信息</text>
        </view>
        <view class="form">
          <u--form labelPosition="left">
            <u-form-item :label-width="'180rpx'" label="结算账户" borderBottom required>
              <u-row>
                <u-col :span="retailOrderInfo.payment_method_name ? 10.5 : 12">
                  <u--input v-model="retailOrderInfo.payment_method_name" border="none" placeholder="请选择结算账户"
                    inputAlign="right" disabled disabledColor="#fff" @tap="openAccountSelector()"></u--input>
                </u-col>
                <u-col span="0.5"></u-col>
                <u-col span="1">
                  <view @click="removePaymentMethodName">
                    <i-close-one theme="filled" size="20" fill="#d13b3b" v-if="retailOrderInfo.payment_method_name" />
                  </view>
                </u-col>
              </u-row>
            </u-form-item>
            <u-form-item :label-width="'180rpx'" label="合计金额" borderBottom>
              <u--input v-model="retailOrderInfo.total_amount" border="none" placeholder="0" inputAlign="right" disabled
                disabledColor="#fff" />
            </u-form-item>
            <u-form-item :label-width="'180rpx'" label="优惠率" borderBottom>
              <u--input v-model="retailOrderInfo.discountRate" border="none" placeholder="0" inputAlign="right"
                :disabled="isView" disabledColor="color: #fff" @input="changeDiscount" />
            </u-form-item>
            <u-form-item :label-width="'180rpx'" label="收款优惠" borderBottom>
              <u--input v-model="retailOrderInfo.discount" border="none" placeholder="0" inputAlign="right"
                :disabled="isView" disabledColor="color: #fff" @input="changeDiscountRate" />
            </u-form-item>
            <u-form-item :label-width="'180rpx'" label="收款金额" borderBottom>
              <u--input v-model="retailOrderInfo.total_sale_price" border="none" placeholder="0" inputAlign="right"
                disabled disabledColor="color: #fff" @input="changePayAmount" />
            </u-form-item>
          </u--form>
        </view>
      </view>

      <!-- 备注 -->
      <view class="info">
        <view class="info_title">
          <text>备注</text>
        </view>
        <view class="remark">
          <u--textarea v-model="retailOrderInfo.remark" placeholder="请输入备注" autoHeight border="none"></u--textarea>
        </view>
      </view>

      <!-- 附件信息 -->
      <view>
        <imageUpload />
      </view>

      <!-- 底部操作栏 -->
      <view class="operation" v-if="!isView">
        <u-button type="primary" text="保存" @click="save"></u-button>
      </view>
    </scroll-view>
    <!-- 语音输入组件 -->
    <view class="input-area">
      <Input style="height: 100%" :isShowCamera="true" />
    </view>
    <!-- 交互组件 -->
    <interactive :isShowAddBtn="true" :jumpToId="4" />

    <!-- 保存提示框 -->
    <view>
      <u-modal :show="promptShow" :title="promptTitle" :content="promptContent" :showCancelButton="true"
        :closeOnClickOverlay="true" @confirm="addRetailOrders" @cancel="close" @close="close"></u-modal>
    </view>

    <!-- 客户选择器 -->
    <searchSelector :selectorShow.sync="selectorShow" :list="selectList" :selectType="selectType"
      @confirm="selectCustomer" @close="closePopup" @customerScrollToLower="getCustomersList" />

    <!-- 库存弹出层 -->
    <inventorySelection :inventorySelectionShow="inventorySelectionShow" :isSelect="true"
      @close="closeInventorySelection" @save="confirmInventory" />

    <!-- 结算账户弹出层 -->
    <settlementAccount :accountSelectShow="accountSelectShow" @close="closeAccountSelector" @confirm="handleAccount" />

    <!-- 商品详细信息弹出层 -->
    <productDetails :productDetailsShow="productDetailsShow" :type="6" :productData="productData"
      :isOpenFromOrder="true" :isShowDelBtn="true" :isShowUnit="false" @delBtnPricing="handleDelBtnPricing"
      @close="closeProductDetails" @confirm="handleProductDetails" />
    
    <!-- 关联订单选择弹出层 -->
    <!-- <wholesaleAssociation ref="wholesaleAssociation" /> -->
  </view>
</template>

<script>
import imageUpload from "@/components/imageUpload/imageUpload.vue";
import Input from "@/components/input/input.vue";
import interactive from "@/components/interactive.vue";
import inventorySelection from "@/components/inventorySelection/inventorySelection.vue";
import settlementAccount from "@/components/settlementAccount/settlementAccount.vue";
import MerchBill from "@/components/merchbill.vue";
import { getCustomerList } from "@/api/customer.js";
import { addRetailOrders } from "@/api/retailOrders.js";
import searchSelector from "@/components/searchSelector.vue";
import productDetails from "@/components/productDetails.vue";
import { getGoodsDetail } from "@/api/goods";
import eventBus from "@/utils/eventBus";
import wholesaleAssociation from "@/pages/wholesaleOrders/editWholesaleOrders/wholesaleAssociation.vue";

export default {
  components: {
    imageUpload,
    Input,
    interactive,
    inventorySelection,
    settlementAccount,
    searchSelector,
    productDetails,
    MerchBill,
    wholesaleAssociation
  },
  data() {
    return {
      isView: false, //是否为修改进入
      promptShow: false, //提示显示状态
      promptTitle: "温馨提示", //提示标题
      promptContent: "保存后将无法修改，是否确认保存？", //保存提示内容
      productDetailsShow: false, //商品详细信息弹出层
      productData: {}, //物品信息
      accountSelectShow: false,
      isExpandGoodsList: false,
      activeQtyIndex: -1,
      inventorySelectionShow: false, // 仓库选择弹出层显示状态
      selectType: 0, //搜索类型 0 客户
      selectorShow: false, //客户选择框展示状态
      selectList: [], //客户选择框数据
      customerList: [], //客户列表
      isCustomerMore: true, //客户列表是否有更多数据
      customerPageParams: {
        //获取客户列表的参数
        page: 1,
        page_size: 20,
      },
      retailOrderInfo: {
        document_type: "wholesale",
        customer_name: "",
        customer: "",
        out_date: "",
        order_no: "",
        related_order_code: "", // 关联订单编号
        related_order_name: "", // 关联订单名称
        related_order_id: "", // 关联订单ID
        sales_order: "", // 关联的销售订单ID（用于API提交）
        warehouse: "",
        warehouse_name: "",
        handler: "", // 操作员ID
        handler_name: "", // 操作员名称
        items: [],
        total_amount: 0,
        discountRate: 0, //优惠率
        discount: 0, //优惠金额
        total_sale_price: 0,
        payment_method: "",
        payment_method_name: "",
        remark: "",
        is_draft: false,
      },
      rules: {
        customer_name: {
          type: "string",
          required: true,
          message: "请选择客户",
          trigger: ["blur", "change"],
        },
        warehouse_name: {
          type: "string",
          required: true,
          message: "请选择仓库",
          trigger: ["blur", "change"],
        },
      },
    };
  },
  onReady() {
    //onReady 为uni-app支持的生命周期之一
    this.$refs.uForm.setRules(this.rules);
  },
  mounted() {
    this.getCustomersList();
    eventBus.$on("selectGoodsList", this.handleSelectGoodsList);
    eventBus.$on("relatedOrderSelected", this.handleRelatedOrder);
    eventBus.$on("relatedOrderItemsSelected", this.handleRelatedOrderItems);
    // document.addEventListener('click', this.handleGlobalClick);
  },
  beforeDestroy() {
    eventBus.$off("selectGoodsList", this.handleSelectGoodsList);
    eventBus.$off("relatedOrderSelected", this.handleRelatedOrder);
    eventBus.$off("relatedOrderItemsSelected", this.handleRelatedOrderItems);
    // document.removeEventListener('click', this.handleGlobalClick);
  },
  onLoad(options) {
    console.log('编辑页面 onLoad options:', options);

    // 如果有传入订单ID，则加载订单详情进行编辑
    if (options.data) {
      try {
        // 先解码 URL 编码的数据，再解析 JSON
        const decodedData = decodeURIComponent(options.data);
        console.log('解码后的数据:', decodedData);

        const data = JSON.parse(decodedData);
        console.log('解析后的订单数据:', data);

        this.retailOrderInfo = data;
        this.retailOrderInfo.total_amount = Number(data.discount) + Number(data.total_sale_price);
        this.retailOrderInfo.discountRate = (Number(data.discount) / Number(data.total_amount)) * 100;

        // 设置页面标题为编辑模式
        uni.setNavigationBarTitle({
          title: "编辑批发订单",
        });
        this.isView = true;

        console.log('编辑模式初始化完成');
      } catch (error) {
        console.error('解析订单数据失败:', error);
        uni.showToast({
          title: '数据解析失败',
          icon: 'none'
        });
        // 解析失败时，按新增模式处理
        uni.setNavigationBarTitle({
          title: "新增批发订单",
        });
      }
    } else {
      // 设置页面标题为新增模式
      console.log('新增模式初始化');
      uni.setNavigationBarTitle({
        title: "新增批发订单",
      });
    }

    const date = new Date();
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    this.retailOrderInfo.out_date = `${year}-${month}-${day}`;
  },
  methods: {
    // 关闭商品详情弹出层
    closeProductDetails() {
      this.productDetailsShow = false;
    },
    // 打开商品详情弹出层
    openProductDetails(item) {
      if (this.isView) {
        uni.showToast({
          title: '保存后禁止修改',
          icon: "none",
        })
        return;
      }

      // 直接循环this.retailOrderInfo.items，计算相同id的商品总用量
      let totalUsedStock = 0;
      this.retailOrderInfo.items.forEach(items => {
        if (items.item === item.item) {
          totalUsedStock += Number(items.quantity || 0) * Number(items.conversion_rate || 1);
        }
      });
      getGoodsDetail(item.item).then((res) => {
        if (res.code == 0) {
          this.productData = { ...item, ...res.data };
          this.productData.remaining_stock = item.remaining_stock;
          console.log(this.productData);
          this.productDetailsShow = true;
        }
      });
    },

    //处理商品详细提交回来的数据
    handleProductDetails(data) {
      console.log(data);
      console.log(this.retailOrderInfo.items);
      data.price = Number(data.purchase_price) || 0;
      data.quantity = Number(data.quantity) || 1;
      data.total = Number(data.total) || 0;
      const targetIndex = this.retailOrderInfo.items.findIndex(
        (infoItem) => infoItem.item === data.item && infoItem.unit === data.unit
      );

      if (targetIndex !== -1) {
        console.log('匹配到相同商品和单位类型，更新数据');
        console.log(this.retailOrderInfo.items[targetIndex]);

        // 使用 $set 确保响应式更新
        this.$set(this.retailOrderInfo.items, targetIndex, data);

        // 直接循环this.retailOrderInfo.items，计算相同id的商品总用量
        let totalUsedStock = 0;
        this.retailOrderInfo.items.forEach(item => {
          if (item.code === data.code) {
            totalUsedStock += Number(item.quantity || 0) * Number(item.conversion_rate || 1);
          }
        });

        // 将计算出的remainingStock赋值给所有相同id的元素
        this.retailOrderInfo.items.forEach(item => {
          if (item.code === data.code) {
            item.remaining_stock = totalUsedStock;
          }
        });

        console.log('更新后的数据:', this.retailOrderInfo.items[targetIndex]);
      } else {
        console.log('未找到匹配的商品');
      }

      // this.calcTotalAndDiscount();
    },
    //商品清单删除商品
    handleDelBtnPricing(data) {
      this.retailOrderInfo.items = this.retailOrderInfo.items.filter(
        (item) => item.item !== data.item
      );
      this.productDetailsShow = false;
      // this.calcTotalAndDiscount();
    },

    //选择商品
    handleSelectGoodsList(data) {
      console.log("选择商品数据：", data);

      // 将选中的商品数据转换为销售订单所需格式
      const formattedItems = data.map((item) => ({
        ...item,
        // 确保商品数据字段与salesOrder.items结构匹配
        // 重新映射并转换为数字类型
        price: Number(item.purchase_price) || 0,
        quantity: Number(item.quantity) || 1,
        total: Number(item.total) || 0,
      }));

      // 遍历新选择的商品，进行合并或添加
      formattedItems.forEach((newItem) => {
        // 查找是否已存在相同商品和相同单位的项目
        // 使用商品ID和单位ID作为唯一标识，确保只有相同物品相同单位才会覆盖
        const existingItemIndex = this.retailOrderInfo.items.findIndex(
          (existingItem) =>
            existingItem.id === newItem.id &&
            existingItem.unit === newItem.unit
        );

        if (existingItemIndex !== -1) {
          // 如果找到相同商品和相同单位，则用新商品覆盖原有商品
          this.$set(this.retailOrderInfo.items, existingItemIndex, newItem);

          console.log(
            `覆盖商品: ${newItem.item_name} (${newItem.unit_name}), 新数量: ${newItem.quantity}`
          );
        } else {
          // 如果没有找到相同商品和单位的组合，则添加为新项目
          this.retailOrderInfo.items.push(newItem);
          console.log(
            `添加新商品: ${newItem.item_name} (${newItem.unit_name}), 数量: ${newItem.quantity}`
          );
        }
      });

    },

    showQtyArrows(index) {
      if (this.isView) {
        uni.showToast({
          title: '保存后禁止修改',
          icon: "none",
        })
        reutrn;
      }
      if (this.activeQtyIndex === index) {
        // 如果已经是当前，说明是第二次点击，收起
        this.activeQtyIndex = -1;
      } else {
        // 否则显示
        this.activeQtyIndex = index;
      }
    },
    hideQtyArrows() {
      this.activeQtyIndex = -1;
    },
    changeQuantity(item, val) {
      // 只更新当前操作的条目quantity
      const currentItemIndex = this.retailOrderInfo.items.findIndex(
        (infoItem) => infoItem.code === item.code && infoItem.unit === item.unit
      );
      if (currentItemIndex === -1) return;
      const currentItem = this.retailOrderInfo.items[currentItemIndex];

      // 验证库存限制
      if (val > Number(currentItem.stock_remaining)) {
        uni.showToast({ title: "数量大于库存余量", icon: "none", mask: true });
        val = Number(currentItem.stock_remaining);
      } else if (val * Number(currentItem.conversion_rate) > Number(currentItem.total_stock)) {
        uni.showToast({ title: "数量大于库存总量", icon: "none", mask: true });
        val = Number(currentItem.total_stock) / Number(currentItem.conversion_rate);
      }

      // 更新当前条目的quantity
      currentItem.quantity = val;

      // 直接循环this.retailOrderInfo.items，计算相同id的商品总用量
      let totalUsedStock = 0;
      this.retailOrderInfo.items.forEach(item => {
        if (item.code === currentItem.code) {
          totalUsedStock += Number(item.quantity || 0) * Number(item.conversion_rate || 1);
        }
      });

      // 将计算出的remainingStock赋值给所有相同id的元素
      this.retailOrderInfo.items.forEach(item => {
        if (item.code === currentItem.code) {
          item.remaining_stock = totalUsedStock;
        }
      });
      // this.calcTotalAndDiscount();
    },
    decreaseQty(index, item) {
      if (!this.retailOrderInfo || !this.retailOrderInfo.items) return;
      const currentItem = this.retailOrderInfo.items[index];
      if (currentItem && Number(currentItem.quantity) > 1) {
        // 只减少当前条目的quantity
        currentItem.quantity = Number(currentItem.quantity) - 1;

        // 计算所有相同code商品的总用量（考虑各自的conversion_rate）
        const sameCodeItems = this.retailOrderInfo.items.filter(i => i.code === currentItem.code);
        const totalStock = sameCodeItems[0].total_stock;

        // 直接循环this.retailOrderInfo.items，计算相同id的商品总用量
        let totalUsedStock = 0;
        this.retailOrderInfo.items.forEach(item => {
          if (item.code === currentItem.code) {
            totalUsedStock += Number(item.quantity || 0) * Number(item.conversion_rate || 1);
          }
        });

        // 将计算出的remainingStock赋值给所有相同id的元素
        this.retailOrderInfo.items.forEach(item => {
          if (item.code === currentItem.code) {
            item.remaining_stock = totalUsedStock;
          }
        });
        // this.calcTotalAndDiscount();
      } else {
        uni.showToast({ title: "数量不能小于1", icon: "none" });
      }
    },
    increaseQty(index, item) {
      if (!this.retailOrderInfo || !this.retailOrderInfo.items) return;
      if (item) {
        const currentItem = this.retailOrderInfo.items[index];
        // 验证库存限制
        if (Number(currentItem.quantity) == Number(currentItem.stock_remaining)) {
          uni.showToast({ title: "数量大于库存余量", icon: "none", mask: true });
          return;
        } else if (
          Number(currentItem.quantity * currentItem.conversion_rate) >= Number(currentItem.total_stock)
        ) {
          uni.showToast({ title: "数量大于库存总量", icon: "none", mask: true });
          currentItem.quantity = Number(currentItem.total_stock) / Number(currentItem.conversion_rate);
        } else {
          // 只增加当前条目的quantity
          currentItem.quantity = Number(currentItem.quantity || 0) + 1;
        }

        // 计算所有相同code商品的总用量（考虑各自的conversion_rate）
        const sameCodeItems = this.retailOrderInfo.items.filter(i => i.code === currentItem.code);
        const totalStock = sameCodeItems[0].total_stock;

        // 直接循环this.retailOrderInfo.items，计算相同id的商品总用量
        let totalUsedStock = 0;
        this.retailOrderInfo.items.forEach(item => {
          if (item.code === currentItem.code) {
            totalUsedStock += Number(item.quantity || 0) * Number(item.conversion_rate || 1);
          }
        });

        // 将计算出的remainingStock赋值给所有相同id的元素
        this.retailOrderInfo.items.forEach(item => {
          if (item.code === currentItem.code) {
            item.remaining_stock = totalUsedStock;
          }
        });
        // this.calcTotalAndDiscount();
      }
    },


    //从商品清单组件回来的数据
        // 处理金额变化
    handleAmountChange(amountData) {
      console.log(amountData);
      this.retailOrderInfo.total_amount = amountData.totalAmount;
      this.calcDiscount();
      this.calcDiscountRate();
      
    },
    /**
     * 计算折扣金额
     * 根据订单总金额和折扣率计算折扣金额，并保留两位小数
     */
    calcDiscountRate() {
      const total = Number(this.retailOrderInfo.total_amount) || 0;
      const discountRate = Number(this.retailOrderInfo.discountRate) || 0;
      console.log(discountRate);

      this.retailOrderInfo.discount = ((total * discountRate) / 100).toFixed(2);
    },
    /**
     * 计算折扣率
     * 根据订单总金额和折扣金额计算折扣率，并保留两位小数
     * 若总金额为 0，则折扣率设为 0
     */
    calcDiscount() {
      const total = Number(this.retailOrderInfo.total_amount) || 0;
      const discount = Number(this.retailOrderInfo.discount) || 0;
      if (total > 0) {
        this.retailOrderInfo.discountRate = ((discount / total) * 100).toFixed(2);
      } else {
        this.retailOrderInfo.discountRate = "0";
      }
      this.calcActualAmount();
    },
    /**
     * 计算实际支付金额
     * 用订单总金额减去折扣金额得到实际支付金额，并保留两位小数
     */
    calcActualAmount() {
      const total = Number(this.retailOrderInfo.total_amount) || 0;
      const discount = Number(this.retailOrderInfo.discount) || 0;
      const actualAmount = total - discount;
      this.retailOrderInfo.total_sale_price = actualAmount.toFixed(2);
      this.retailOrderInfo.total_amount = total.toFixed(2);
    },
    /**
     * 当折扣率变化时更新相关数据
     * 先计算折扣金额，再更新总金额、实际支付金额、已支付金额和欠款金额
     */
    changeDiscount() {
      this.calcDiscountRate();
      this.calcActualAmount();
    },
    /**
     * 当折扣金额变化时更新相关数据
     * 先计算折扣率，再更新总金额、实际支付金额、已支付金额和欠款金额
     */
    changeDiscountRate() {
      this.calcDiscount();
      this.calcActualAmount();
    },

    // 获取客户列表
    getCustomersList() {
      console.log("开始获取客户列表，参数：", this.customerPageParams);
      if (!this.isCustomerMore) return;
      getCustomerList(this.customerPageParams)
        .then((res) => {
          console.log("客户列表API返回结果：", res);
          if (res.code == 0) {
            this.customerList = res.data.results;
            console.log(
              "客户列表加载成功，共",
              this.customerList.length,
              "条数据"
            );
            if (this.customerPageParams.page_size > res.data.results.length) {
              this.isCustomerMore = false;
            } else {
              this.customerPageParams.page++;
              this.isCustomerMore = true;
            }
          } else {
            console.error("客户列表加载失败：", res.msg);
            uni.showToast({
              title: res.msg,
              icon: "none",
            });
          }
        })
        .catch((err) => {
          console.error("客户列表接口调用失败：", err);
        });
    },
    //打开客户选择框
    openSelector(index) {
      if (this.isView) {
        return;
      }
      console.log("用户点击了选择客户，选择类型：", index);
      this.selectType = index;
      // 如果客户列表为空，先拉取数据
      if (this.selectType === 1 && this.customerList.length === 0) {
        console.log("客户列表为空，正在拉取数据...");
        this.getCustomersList();
      }
      this.selectorShow = true;
      console.log("选择器已打开，当前状态：", this.selectorShow);
      if (this.selectType === 1) {
        this.selectList = this.customerList;
        console.log("客户选择列表数据：", this.selectList);
      } else {
        this.selectList = this.settlementAccountList;
      }
    },
    // 选择客户
    selectCustomer(item) {
      console.log("用户选择了客户：", item);
      if (this.selectType === 1) {
        this.retailOrderInfo.customer = item.id;
        this.retailOrderInfo.customer_name = item.name;
        // 手动触发客户字段验证
        this.$nextTick(() => {
          this.$refs.uForm.validateField("customer_name");
        });
      }
    },
    // 关闭搜索弹出层
    closePopup() {
      console.log("用户关闭了选择器");
      this.selectorShow = false;
    },

    // 打开库存选择弹出层
    openInventorySelection() {
      if (this.isView) {
        uni.showToast({
          title: '保存后禁止修改',
          icon: "none",
        })
        return;
      }
      this.inventorySelectionShow = true;
    },
    // 关闭库存选择弹出层
    closeInventorySelection() {
      this.inventorySelectionShow = false;
    },
    // 接收库存弹出层的数据
    confirmInventory(data) {
      console.log('confirmInventory 接收到的仓库数据:', data);

      this.retailOrderInfo.warehouse = data.id;
      this.retailOrderInfo.warehouse_name = data.name;
      uni.setStorageSync("retailWarehouseId", data.id);

      console.log('设置后的 retailOrderInfo 仓库信息:', {
        warehouse: this.retailOrderInfo.warehouse,
        warehouse_name: this.retailOrderInfo.warehouse_name
      });
    },

    // 添加商品
    addGoods(index) {
      if (this.isView) {
        uni.showToast({
          title: '保存后禁止修改',
          icon: "none",
        })
        return;
      }
      if (!this.retailOrderInfo.warehouse) {
        uni.showToast({
          title: '请先选择仓库',
          icon: "none",
        });
        return;
      }
      uni.navigateTo({
        url:
          "/pages/purchase/editPurchase/components/productSelection?type=6&isShowWarehouse=true&warehouse=" +
          this.retailOrderInfo.warehouse +
          "&warehouse_name=" +
          this.retailOrderInfo.warehouse_name,
      });
    },

    //打开结算账户选择
    openAccountSelector() {
      if (this.isView) {
        uni.showToast({
          title: '保存后禁止修改',
          icon: "none",
        })
        return;
      }
      this.accountSelectShow = true;
    },
    //删除结算账户
    removePurchaseOrder() {
      this.retailOrderInfo.payment_method = "";
      this.retailOrderInfo.payment_method_name = "";
    },
    //处理结算账户的数据
    handleAccount(data) {
      this.retailOrderInfo.payment_method = data.id;
      this.retailOrderInfo.payment_method_name = data.account_name;
    },
    //关闭结算账户选择
    closeAccountSelector() {
      this.accountSelectShow = false;
    },

    // 打开保存提示框逻辑
    save() {
      // 先检查商品列表
      if (
        !this.retailOrderInfo.items ||
        this.retailOrderInfo.items.length === 0
      ) {
        uni.showToast({
          title: "请至少添加一个商品",
          icon: "none",
        });
        return;
      }
      //检查结算账户
      if (
        this.retailOrderInfo.pay_amount > 0 &&
        !this.retailOrderInfo.payment_method
      ) {
        uni.showToast({
          title: "请填写结算账户",
          icon: "none",
        });
        return;
      }
      // 触发表单校验
      this.$refs.uForm
        .validate()
        .then((valid) => {
          if (valid) {
            // 校验通过
            this.promptShow = true;
          } else {
            // 校验不通过
            uni.showToast({ title: "信息未填写完整", icon: "none" });
          }
        })
        .catch((error) => {
          console.error("表单验证错误:", error);
          uni.showToast({ title: "信息未填写完整", icon: "none" });
        });
    },
    //关闭提示框
    close() {
      this.promptShow = false;
    },

    //新建逻辑
    addRetailOrders() {
      // 构建符合API要求的数据结构
      const submitData = this.buildSubmitData();
      console.log('提交的数据:', submitData);

      addRetailOrders(submitData).then((res) => {
        if (res.code == 0) {
          console.log(res);
          uni.showToast({
            title: "保存成功",
            icon: "success",
          });
          uni.navigateBack({
            delta: 1,
          });
        }
      });
    },

    // 构建提交数据，确保格式符合API要求
    buildSubmitData() {
      const data = {
        customer: parseInt(this.retailOrderInfo.customer) || null,
        warehouse: parseInt(this.retailOrderInfo.warehouse) || null,
        document_type: this.retailOrderInfo.document_type,
        handler: parseInt(this.retailOrderInfo.handler) || null, // 操作员ID，如果未设置则为null
        total_sale_price: parseFloat(this.retailOrderInfo.total_sale_price) || 0,
        discount: parseFloat(this.retailOrderInfo.discount) || 0,
        payment_method: parseInt(this.retailOrderInfo.payment_method) || null,
        is_draft: Boolean(this.retailOrderInfo.is_draft),
        remark: this.retailOrderInfo.remark || "",
        items: this.retailOrderInfo.items.map(item => ({
          item: parseInt(item.item),
          unit: parseInt(item.unit),
          quantity: parseFloat(item.quantity) || 1,
          price: parseFloat(item.price) || 0
        }))
      };

      // 如果有关联的销售订单，添加sales_order字段
      if (this.retailOrderInfo.sales_order) {
        data.sales_order = parseInt(this.retailOrderInfo.sales_order);
      }

      return data;
    },

    //删除仓库
    removeWarehouseName() {
      if (this.isView) {
        uni.showToast({
          title: '保存后禁止修改',
          icon: "none",
        })
        return;
      }
      this.retailOrderInfo.warehouse = "";
      this.retailOrderInfo.warehouse_name = "";
      this.retailOrderInfo.items = [];
      uni.removeStorageSync("retailWarehouseId");
    },
    //删除结算账户
    removePaymentMethodName() {
      this.retailOrderInfo.payment_method = "";
      this.retailOrderInfo.payment_method_name = "";
    },
    // 打开关联订单选择弹出层
    openRelatedOrder() {
      if (this.isView) {
        uni.showToast({
          title: '保存后禁止修改',
          icon: "none",
        })
        return;
      }
      
      // 检查是否已选择客户和仓库
      if (!this.retailOrderInfo.customer) {
        uni.showToast({
          title: '请先选择客户',
          icon: "none",
        });
        return;
      }
      
      // 构建传递给关联订单选择页面的参数
      console.log('当前 retailOrderInfo 的仓库信息:', {
        warehouse: this.retailOrderInfo.warehouse,
        warehouse_name: this.retailOrderInfo.warehouse_name
      });

      const params = {
        customer: this.retailOrderInfo.customer,
        warehouse: this.retailOrderInfo.warehouse || '',
        warehouse_name: this.retailOrderInfo.warehouse_name || '',
        warehouse_id: this.retailOrderInfo.warehouse || '' // 添加warehouse_id用于API调用
      };

      console.log('传递给关联页面的参数:', params);

      uni.navigateTo({
        url: `/pages/wholesaleOrders/editWholesaleOrders/wholesaleAssociation?returnGoodsInfo=${JSON.stringify(params)}`
      });
    },
    // 接收关联订单弹出层的数据
    handleRelatedOrder(data) {
      console.log('接收到关联订单数据:', data);
      this.retailOrderInfo.related_order_id = data.id;
      this.retailOrderInfo.related_order_code = data.order_id || data.id;
      this.retailOrderInfo.related_order_name = data.customer_name;
      this.retailOrderInfo.sales_order = data.id; // 设置API需要的sales_order字段

      // 如果还没有选择客户，自动设置为关联订单的客户
      if (!this.retailOrderInfo.customer && data.customer) {
        this.retailOrderInfo.customer = data.customer;
        this.retailOrderInfo.customer_name = data.customer_name;
      }

      // 如果还没有选择仓库，自动设置为关联订单的仓库
      if (!this.retailOrderInfo.warehouse && data.warehouse) {
        this.retailOrderInfo.warehouse = data.warehouse;
        this.retailOrderInfo.warehouse_name = data.warehouse_name;
      }

      // 如果关联订单有操作员信息，也可以设置
      if (data.handler) {
        this.retailOrderInfo.handler = data.handler;
        this.retailOrderInfo.handler_name = data.handler_name;
      }
    },
    // 删除关联订单
    removeRelatedOrder() {
      this.retailOrderInfo.related_order_code = "";
      this.retailOrderInfo.related_order_name = "";
      this.retailOrderInfo.related_order_id = "";
      this.retailOrderInfo.sales_order = ""; // 清除API字段
    },

    // 处理关联订单的商品列表
    handleRelatedOrderItems(items) {
      console.log('接收到关联订单商品列表:', items);

      if (!items || items.length === 0) {
        uni.showToast({
          title: '关联订单没有商品信息',
          icon: 'none'
        });
        return;
      }

      // 这里只是记录日志，实际的商品选择会通过商品选择页面完成
      console.log(`关联订单包含 ${items.length} 个商品，用户将在商品选择页面进行选择`);
    }
  },
};
</script>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

.content {
  overflow: auto;
}

.info {
  background-color: #fff;
  margin-bottom: 20rpx;
}

.info_title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  border-bottom: 1px solid #eee;
  font-size: 28rpx;
}

.form {
  padding: 0 20rpx;
  width: 95%;
  margin: 0 auto;
}

.goods-list {
  padding: 0 20rpx;
}

.goods_item {
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.03);
  margin-bottom: 20rpx;
  padding: 0 0 0 0;
}

.goods_item_click {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 10px 0;
  position: relative;
}

.goods_img {
  width: 48px;
  height: 48px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.goods_img image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.goods_main {
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex: 1;
}

.goods_name {
  font-size: 16px;
  font-weight: bold;
  color: #222;
}

.goods_id {
  font-size: 12px;
  color: #888;
  margin: 2px 0;
}

.goods_extra {
  font-size: 12px;
  color: #888;
  margin-bottom: 2px;
  display: flex;
  align-items: center;
}

.goods_price {
  display: flex;
  align-items: center;
  margin-top: 2px;
}

.price {
  color: #e22;
  font-size: 14px;
  font-weight: bold;
  margin-right: 2px;
}

.unit {
  color: #e22;
  font-size: 12px;
}



.qty_x {
  font-size: 15px;
  font-weight: bold;
  margin-right: 2px;
}

.qty_control {
  display: flex;
  align-items: center;
  position: relative;
}

.qty_input {
  width: 40px;
  height: 22px;
  border: 1px solid #222;
  border-radius: 3px;
  text-align: center;
  font-size: 14px;
  background: #fff;
  color: #222;
  margin-left: 2px;
}

.arrow {
  font-size: 18px;
  color: #888;
  margin-left: 10px;
}

.total-info {
  font-size: 28rpx;
}

.total-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10rpx;
  padding: 10rpx 0;
  border-bottom: 1px solid #eee;
  margin-left: 30rpx;

  .total-row-right {
    display: flex;
    align-items: center;
  }

  text:nth-child(1) {
    width: 80px;
    margin-left: 30rpx;
  }
}

.telescoping {
  display: flex;
  justify-content: center;
  align-items: center;
}

.remark {
  padding: 20rpx;
  width: 95%;
  margin: 0 auto;
}

.upload-area {
  padding: 20rpx;
  width: 95%;
  margin: 0 auto;
}

.add-icon {
  padding: 10rpx;
  cursor: pointer;
}

.operation {
  width: 90%;
  height: 80rpx;
  margin: 10rpx auto;
  padding: 10rpx 20rpx;
  background-color: #fff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}

::v-deep .operation .u-button {
  height: 30px;
  width: 25%;
  position: relative;
  align-items: center;
  justify-content: center;
  display: flex;
  flex-direction: row;
  box-sizing: border-box;
}

::v-deep .u-input--radius.data-v-fdbb9fe6,
.u-input--square.data-v-fdbb9fe6 {
  border-radius: 4px;
  padding-top: 0 !important;
  padding-left: 0 !important;
  padding-bottom: 0 !important;
  padding-right: 0 !important;
}

::v-deep .u-input__content__field-wrapper__field.data-v-fdbb9fe6 {
  line-height: 26px;
  text-align: left;
  color: #303133;
  height: 24px;
  font-size: 15px;
  flex: 1;
  width: 125rpx;
}

::v-deep .u-form-item__body__left__content__required.data-v-5e7216f1 {
  position: absolute;
  left: 0px;
  color: #f56c6c;
  line-height: 20px;
  font-size: 20px;
  top: 3px;
}

::v-deep .u-form-item__body__left__content__label.data-v-5e7216f1 {
  display: flex;
  flex-direction: row;
  align-items: center;
  flex: 1;
  margin-left: 30rpx;
}

.stock-tag {
  display: inline-block;
  border: 1px solid #ffa940;
  color: #ffa940;
  border-radius: 12px;
  font-size: 20rpx;
  padding: 0 12rpx;
  margin-left: 10rpx;
  line-height: 24rpx;
  height: 24rpx;
  vertical-align: middle;
}
</style>

