<template>
  <view class="container">
    <!-- 搜索组件 -->
    <search :searchType="1" @search-result="handleSearchResult" />

    <!-- 调试信息 -->
    <!-- <view class="debug-info" style="background: #f0f0f0; padding: 10rpx; margin: 10rpx; font-size: 24rpx;">
      <text>仓库ID: {{ warehouse || '未设置' }} | 客户ID: {{ customer || '未设置' }} | 订单数量: {{ salesOrderList.length }}</text>
    </view> -->

    <!-- 销售订单列表 -->
    <scroll-view scroll-y class="sales-list" :style="{ height: scrollViewHeight }" @scrolltolower="onScrollToLower">
      <view v-for="(item, index) in salesOrderList" :key="index" @tap="selectSalesOrder(item)"
        :class="{ 'sales-item-disabled': !item.isWarehouse && !item.isCustomer }">
        <view class="sales-item attribute_font" v-if="item.order_status"
          :class="{ 'item-unmatched': !item.isWarehouse || !item.isCustomer }">
          <!-- 单据状态标签 -->
          <view class="status-order-tag">
            {{ getOrderStatusText(item.order_status) }}
          </view>
          <view class="status-payment-tag">
            {{ getReceiptStatusText(item.receipt_status) }}
          </view>
          <!-- 右侧添加按钮 -->
          <view class="item-content-container">
            <view class="item-content">
              <view class="info-row">
                <text class="label">客户</text>
                <text class="value">：{{ item.customer_name }}</text>
              </view>
              <view class="info-row">
                <text class="label">商品</text>
                <text class="value">：{{ item.short_desc || item.item_names || '暂无商品' }}</text>
              </view>
              <view class="info-row">
                <view class="info-row-item">
                  <text class="label">操作员</text>
                  <text class="value">：{{ item.handler_name || '未指定' }}</text>
                </view>
                <view>
                  <text class="sub-label">数量</text>
                  <text class="value">：{{ getTotalQuantity(item.items) }}</text>
                </view>
              </view>
              <view class="info-row">
                <view class="info-row-item">
                  <text class="label">金额合计</text>
                  <text class="value">：{{ item.total_amount }}</text>
                </view>
              </view>

            </view>
            <view class="add-btn" @click.stop="selectSalesOrder(item)">
              <i-add-one theme="outline" size="20" fill="#378ce5" v-if="!item.selected" />
            </view>
          </view>
          <view class="info-bottom">
            <view class="info-row" style="margin-bottom: 10rpx">
              <text class="label">订单日期</text>
              <text class="value">：{{ formatDate(item.order_date) }}</text>
            </view>
            <view class="info-row" style="margin-bottom: 0">
              <text class="label">订单编号</text>
              <text class="value">：{{ item.order_id || item.id }}</text>
            </view>
          </view>
        </view>
      </view>
      <!-- 加载更多 -->
      <u-loadmore :status="loadStatus" :loading-text="'加载中...'" :loadmore-text="'上拉加载更多'" :nomore-text="'没有更多数据了'" />
    </scroll-view>

    <!-- 购物车遮罩层 -->
    <view v-if="shoppingCartShow" class="cart_mask" @click="expandShoppingCart"></view>

    <!-- 购物车面板 -->
    <view v-if="shoppingCartShow" class="cart_popup" :animation="cartAnimation">
      <view class="cart_container">
        <view class="categoryConfirmationBtn">
          <view>已选商品</view>
          <view class="blueFont" @click="clearSelectGoodsList">清空</view>
        </view>
        <view class="divider"></view>
        <view class="goods">
          <view class="goods_item" v-for="(item, index) in selectGoodsList" :key="index">
            <view class="goods_left">
              <image class="goods_img" :src="item.imgurl || '/static/img/logo.png'" mode=""></image>
              <view class="goods_info">
                <view class="item_name">{{ item.item_name }}</view>
                <view class="goods_code">{{ item.order_id }}</view>
                <view class="goods_price">
                  <text class="price">￥{{ item.price || '未填写' }}</text>
                  <text class="unit">/个</text>
                </view>
              </view>
            </view>
            <view class="goods_num">
              <view class="goods_num_reduce" @click.stop="reduceNum(item)">
                <i-reduce-one theme="outline" size="20" fill="#3894ff" />
              </view>
              <view class="goods_num_input">{{ item.quantity || 0 }}</view>
              <view class="goods_num_add" @click.stop="addNum(item)">
                <i-add-one theme="filled" size="20" fill="#3894ff" />
              </view>
            </view>
          </view>
        </view>
      </view>
      <Input style="height: 100%" />
    </view>

    <!-- 语音输入组件 -->
    <view class="input-area">
      <Input style="height: 100%" :isShowCamera="true" />
    </view>

    <!-- 交互组件 -->
    <interactive :isShowAddBtn="false" :jumpToId="3" />


  </view>
</template>

<script>
import search from "@/components/search.vue";
import Input from "@/components/input/input.vue";
import interactive from "@/components/interactive.vue";
import { getSalesOrderList, getSalesOrderDetail } from "@/api/salesOrder";
import eventBus from "@/utils/eventBus";


export default {
  components: {
    search,
    Input,
    interactive,
  },
  data() {
    return {
      salesOrderList: [],
      // 购物车相关
      shoppingCartShow: false,
      cartAnimation: null,
      selectGoodsList: [], //被选中的商品
      productDetailsShow: "", //是否展示商品详情弹出层
      productData: {}, //商品详情数据
      salesOrderListParams: {
        page: 1,
        page_size: 10,
      },
      hasMore: true,
      loading: false,
      scrollViewHeight: "500px",
      loadStatus: "loadmore", // 'loadmore' | 'loading' | 'nomore'
      warehouse: null,//仓库id
      warehouse_name: '',//仓库名称
      customer: null,//客户id
      warehouse_id: null,//仓库ID（用于API调用）
    };
  },
  onLoad(options) {
    console.log('wholesaleAssociation onLoad options:', options);
    console.log('options.returnGoodsInfo:', options.returnGoodsInfo);

    const data = JSON.parse(options.returnGoodsInfo);
    console.log('解析的关联订单信息:', data);
    console.log('解析后的仓库信息:', {
      warehouse: data.warehouse,
      warehouse_name: data.warehouse_name
    });

    this.warehouse = data.warehouse;
    this.warehouse_name = data.warehouse_name || '';
    this.customer = data.customer;
    this.warehouse_id = data.warehouse_id || data.warehouse; // 设置用于API调用的仓库ID
    console.log('设置的仓库ID:', this.warehouse, '仓库名称:', this.warehouse_name, '客户ID:', this.customer, 'API仓库ID:', this.warehouse_id);
  },
  mounted() {
    this.initScrollViewHeight();
    // 重置分页参数，确保获取最新数据
    this.salesOrderListParams.page = 1;
    this.hasMore = true;
    this.loadStatus = 'loadmore';
    this.getSalesOrderList();
  },
  methods: {
    //获取销售订单列表
    getSalesOrderList(isLoadMore = false) {
      if (this.loading || (!this.hasMore && isLoadMore)) return;
      this.loading = true;
      this.loadStatus = "loading";

      // 构建请求参数，如果选择了客户则添加customer_id筛选
      const params = {
        ...this.salesOrderListParams,
        exclude_draft: true,  // 默认排除暂存状态的订单
        order_status: 'confirmed'  // 只获取已确认的销售订单
      };
      if (this.customer) {
        params.customer_id = this.customer;
      }

      console.log('请求销售订单列表参数:', params);
      getSalesOrderList(params).then((res) => {
        console.log('获取销售订单列表响应:', res);
        if (res.code == 0) {
          const results = res.data.results || [];
          console.log('原始订单数据:', results);
          const newResults = results.map(item => ({
            ...item,
            isWarehouse: true,
            isCustomer: true
          }));
          if (isLoadMore) {
            this.salesOrderList = this.salesOrderList.concat(newResults);
          } else {
            this.salesOrderList = newResults;
          }
          console.log('设置订单列表后长度:', this.salesOrderList.length);
          this.filterItemsForWarehouse();
          // 判断是否有更多
          if (this.salesOrderList.length < (res.data.count || 0)) {
            this.hasMore = true;
            this.loadStatus = "loadmore";
            this.salesOrderListParams.page++;
          } else {
            this.hasMore = false;
            this.loadStatus = "nomore";
          }
        } else {
          uni.showToast({
            title: res.msg,
            icon: "none",
          });
          this.loadStatus = "loadmore";
        }
        this.loading = false;
      }).catch((err) => {
        console.log(err);
        uni.showToast({
          title: "请求失败",
          icon: "none",
        });
        this.loading = false;
        this.loadStatus = "loadmore";
      });
    },

    //收款状态映射（保留以备后用）
    getReceiptStatusText(status) {
      const statusMap = {
        "未收款": "未收款",
        "部分收款": "部分收款",
        "已收款": "已收款",
      };
      return statusMap[status] || status;
    },

    handleSearchResult(newVal) {
      this.salesOrderList = newVal;
    },

    // 获取订单状态文本
    getOrderStatusText(status) {
      const statusMap = {
        'draft': '暂存',
        'pending': '待处理',
        'partial': '部分完成',
        'completed': '已完成',
        'cancelled': '已取消'
      };
      return statusMap[status] || '未知状态';
    },

    // 获取收款状态文本
    getReceiptStatusText(status) {
      const statusMap = {
        'unpaid': '未收款',
        'partial': '部分收款',
        'paid': '已收款'
      };
      return statusMap[status] || '未知';
    },

    // 计算商品总数量
    getTotalQuantity(items) {
      if (!items || !Array.isArray(items)) return 0;
      return items.reduce((total, item) => {
        return total + parseFloat(item.quantity || 0);
      }, 0);
    },

    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      return date.toLocaleDateString('zh-CN');
    },

    // 选择关联销售订单
    selectSalesOrder(item) {
      if (!item.isWarehouse || !item.isCustomer) {
        uni.showToast({
          title: '选择的订单与当前条件不匹配',
          icon: 'none'
        });
        return;
      }

      // 检查是否为草稿状态
      if (item.is_draft) {
        uni.showToast({
          title: '草稿状态的单据不能关联',
          icon: 'none'
        });
        return;
      }

      console.log('选择的销售订单:', item);

      // 构建关联订单数据
      const relatedOrderData = {
        id: item.id,
        order_id: item.order_id || item.id, // 销售订单使用order_id作为订单号
        customer: item.customer,
        customer_name: item.customer_name,
        warehouse: item.warehouse,
        warehouse_name: item.warehouse_name,
        handler: item.handler,
        handler_name: item.handler_name,
        order_date: item.order_date,
        total_amount: item.total_amount,
        total_sale_price: item.total_amount // 使用total_amount作为销售价格
      };

      // 显示成功消息
      uni.showToast({
        title: `已选择订单：${item.order_id}`,
        icon: 'success',
        duration: 2000
      });

      eventBus.$emit('relatedOrderSelected', relatedOrderData); // 发送给编辑页面的事件

      // 获取销售订单详情，跳转到商品选择页面
      this.getSalesOrderDetail(item.id).then(() => {
        // 成功跳转到商品选择页面，不需要返回
        console.log('已跳转到商品选择页面携带的数据：');
        // console.log(relatedOrderData);
        
      }).catch(() => {
        // 获取详情失败时，延迟返回
        setTimeout(() => {
          uni.navigateBack({
            delta: 1
          });
        }, 1500);
      });
    },

    // 获取销售订单详情
    async getSalesOrderDetail(orderId) {
      try {
        uni.showLoading({ title: '关联订单中...' });

        // 构建API参数，如果有仓库ID则添加warehouse_id参数
        const apiParams = {};
        if (this.warehouse_id) {
          apiParams.warehouse_id = this.warehouse_id;
          console.log('调用销售订单详情API，携带仓库ID:', this.warehouse_id);
        }

        const res = await getSalesOrderDetail(orderId, apiParams);

        if (res && res.code === 0 && res.data) {
          const orderDetail = res.data;

          // 处理销售订单商品数据，为批发订单关联做准备
          const processedItems = (orderDetail.items || []).map(orderItem => ({
            ...orderItem,
            // 添加批发订单相关字段
            wholesale_quantity: parseFloat(orderItem.quantity || 0),
            wholesale_price: orderItem.price || orderItem.sale_price,
            originalQuantity: orderItem.quantity,
            // 确保字段兼容性
            item_name: orderItem.item_name || orderItem.name,
            unit_name: orderItem.unit_name || orderItem.unit,
            // 确保价格字段正确
            price: orderItem.price || orderItem.sale_price || orderItem.wholesale_price || 0
          }));

          const combinedData = {
            items: processedItems,
            orderDetail: orderDetail
          };

          console.log('处理后的销售订单数据:', combinedData);

          // 发送完整的关联订单数据给批发订单编辑页面
          eventBus.$emit('relatedOrderItemsSelected', combinedData);

          uni.hideLoading();
          console.log('已跳转到商品选择页面携带的数据：',combinedData);
          
          // 跳转到商品选择页面，传递当前批发订单的仓库信息
          console.log('准备跳转到商品选择页面，当前仓库信息:', {
            warehouse: this.warehouse,
            warehouse_name: this.warehouse_name
          });

          const navigationUrl = "/components/productSelection?data=" +
            encodeURIComponent(JSON.stringify(combinedData)) +
            "&type=5" +
            "&warehouse=" + (this.warehouse || '') +
            "&warehouse_name=" + (this.warehouse_name || '');

          console.log('跳转URL:', navigationUrl);

          uni.navigateTo({
            url: navigationUrl
          });

          // 返回成功的Promise
          return Promise.resolve(combinedData);
        } else {
          uni.hideLoading();
          uni.showToast({
            title: res?.msg || '获取订单详情失败',
            icon: 'none',
            duration: 1500
          });
          return Promise.reject(new Error(res?.msg || '获取订单详情失败'));
        }
      } catch (error) {
        uni.hideLoading();
        console.error('获取销售订单详情失败:', error);
        uni.showToast({
          title: '获取订单详情失败，请重试',
          icon: 'none',
          duration: 1500
        });
        return Promise.reject(error);
      }
    },
    // scrollViewHeight 动态计算
    initScrollViewHeight() {
      try {
        const info = uni.getSystemInfoSync();
        const screenWidth = info.screenWidth;
        const navBarHeight = 44;
        const searchHeight = (screenWidth * 80) / 750;
        const inputHeight = (screenWidth * 90) / 750;
        const totalHeight = navBarHeight + searchHeight + inputHeight;
        const scrollHeight = info.windowHeight - totalHeight;
        this.scrollViewHeight = `${scrollHeight}px`;
      } catch (e) {
        console.error("获取系统信息失败：", e);
      }
    },
    onScrollToLower() {
      if (this.loadStatus === "loadmore") {
        this.getSalesOrderList(true);
      }
    },

    filterItemsForWarehouse() {
      console.log('开始过滤订单，当前仓库ID:', this.warehouse, '客户ID:', this.customer);
      console.log('订单列表长度:', this.salesOrderList.length);

      const matchedItems = [];
      const unmatchedItems = [];

      this.salesOrderList.forEach((item, index) => {
        // 仓库匹配逻辑：如果没有设置仓库条件，或者订单没有仓库字段，或者仓库匹配，则认为匹配
        const warehouseMatch = !this.warehouse || !item.warehouse || item.warehouse == this.warehouse;

        // 客户匹配逻辑：由于API请求时已经根据customer_id筛选，这里的客户都应该匹配
        // 但为了保持兼容性，仍然进行检查
        const customerMatch = !this.customer || item.customer == this.customer;

        console.log(`订单${index} (${item.order_id || item.id}):`, {
          warehouse: item.warehouse,
          customer: item.customer,
          warehouseMatch,
          customerMatch,
          note: this.customer ? 'API已筛选客户' : '未设置客户筛选'
        });

        if (warehouseMatch && customerMatch) {
          item.isWarehouse = true;
          item.isCustomer = true;
          matchedItems.push(item);
        } else {
          item.isWarehouse = warehouseMatch;
          item.isCustomer = customerMatch;
          unmatchedItems.push(item);
        }
      });

      console.log('匹配的订单数量:', matchedItems.length, '不匹配的订单数量:', unmatchedItems.length);
      this.salesOrderList = [...matchedItems, ...unmatchedItems];
    },

    // 格式化日期（保留以备后用）
    formatDate(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      });
    },

  },
};
</script>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  justify-content: space-between;
  background-color: #f5f5f5;
}

.sales-list {
  flex: 1;
  padding: 20rpx;
  width: 95%;
  overflow: auto;
}

.sales-item {
  width: 95%;
  margin: 0 auto 20rpx;
  background-color: #fff;
  border-radius: 8rpx;
  padding: 20rpx;
  position: relative;
}

.status-order-tag {
  position: absolute;
  top: 12rpx;
  right: 140rpx;
  height: 35rpx;
  line-height: 35rpx;
  border: 2rpx solid #e99129;
  color: #e99129;
  border-radius: 8rpx;
  font-size: 22rpx;
  background: #fff;
  font-weight: 500;
  box-sizing: border-box;
  text-align: center;
  width: 110rpx;
}

.status-payment-tag {
  position: absolute;
  top: 12rpx;
  right: 12rpx;
  height: 35rpx;
  line-height: 35rpx;
  border: 2rpx solid #ff4d4f;
  color: #ff4d4f;
  border-radius: 8rpx;
  font-size: 22rpx;
  background: #fff;
  font-weight: 500;
  box-sizing: border-box;
  text-align: center;
  width: 110rpx;
}



.item-content-container {
  display: flex;
  align-items: center;
}

.item-content {
  border-bottom: 1px solid #eee;
  padding-bottom: 10rpx;
  margin-bottom: 10rpx;
  width: 90%;
}

.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  flex-wrap: wrap;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-row-item {
  width: 400rpx;
  display: flex;
  align-items: center;
}

.label {
  color: #333;
  width: 110rpx;
  display: inline-block;
  text-align-last: justify;
  text-align: justify;
  text-justify: distribute-all-lines;
}

.value {
  color: #333;
  margin-right: 20rpx;
}

.sub-label {
  color: #333;
  width: 80rpx;
  display: inline-block;
  text-align-last: justify;
  text-align: justify;
  text-justify: distribute-all-lines;
  margin-right: 10rpx;
}

.info-bottom {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.status-audit-tag {
  position: absolute;
  top: 12rpx;
  right: 12rpx;
  height: 35rpx;
  line-height: 35rpx;
  border: 2rpx solid #ff4d4f;
  color: #ff4d4f;
  border-radius: 8rpx;
  font-size: 22rpx;
  background: #fff;
  font-weight: 500;
  box-sizing: border-box;
  text-align: center;
  width: 110rpx;
}

.payAttentionTo {
  display: flex;
  align-items: center;
}

.cart_container {
  height: 800rpx;
  background-color: #fff;
}

.categoryConfirmationBtn {
  width: 90%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin: 0 auto;
  padding: 20rpx 0;
}

.divider {
  border: 1px solid #ccc;
  width: 100%;
}

.goods_item {
  width: 90%;
  display: flex;
  align-items: center;
  padding: 10px 0;
  margin: 0 auto;
  border-bottom: 1px solid #f0f0f0;
  background: #fff;
}

.goods_left {
  display: flex;
  align-items: center;
  flex: 1;
}

.goods_img {
  width: 48px;
  height: 48px;
  border-radius: 4px;
  margin-right: 12px;
  object-fit: cover;
}

.goods_info {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.item_name {
  font-size: 16px;
  font-weight: bold;
  color: #222;
}

.goods_code {
  font-size: 12px;
  color: #888;
  margin: 2px 0;
}

.goods_price {
  display: flex;
  align-items: center;
  margin-top: 2px;
}

.price {
  color: #e22;
  font-size: 14px;
  font-weight: bold;
  margin-right: 2px;
}

.unit {
  color: #e22;
  font-size: 12px;
}

.goods_num {
  display: flex;
  align-items: center;
  margin-left: 10px;
}

.goods_num_input {
  width: 28px;
  text-align: center;
  font-size: 15px;
  margin: 0 6px;
  color: #222;
}

.cart_mask {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 1000;
}

.cart_popup {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  z-index: 1001;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  max-height: 70vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.08);
}

.cart_container {
  overflow-y: auto;
  max-height: 60vh;
  background-color: #fff;
}

.popup-btn {
  width: 100%;
  position: sticky;
  bottom: 0;
  background: #fff;
  z-index: 10;
  display: flex;
  justify-content: center;
  align-items: center;
}

.operatingButton {
  width: 90%;
  height: 100rpx;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #fff;
  padding: 0 5%;
}

.selectGoods {
  width: 45%;
}

::v-deep .operatingButton .u-button.data-v-3bf2dba7 {
  height: 35px;
  width: 90%;
  position: relative;
  align-items: center;
  justify-content: center;
  display: flex;
  flex-direction: row;
  box-sizing: border-box;
  flex-direction: row;
}

.add-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: #ffffff00;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 20rpx;
}

.sales-item-disabled {
  opacity: 0.7;
  background: #f5f5f5;
}

.item-unmatched {
  opacity: 0.8;
  background: #fff3cd;
  border-left: 4px solid #ffc107;
}
</style>


