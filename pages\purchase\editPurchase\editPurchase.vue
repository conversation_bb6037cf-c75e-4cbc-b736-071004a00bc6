<template>
  <view class="container">
    <!-- 基本信息 -->
    <scroll-view scroll-y class="content">
      <view class="info">
        <view class="info_title">
          <text>基本信息</text>
        </view>
        <view class="form">
          <u--form labelPosition="left">
            <u-form-item :label-width="'180rpx'" label="供应商" borderBottom required>
              <u--input v-model="purchaseInfo.supplier_name" border="none" placeholder="请选择供应商" inputAlign="right"
                disabled disabledColor="#fff" @tap="openSelector(0)"></u--input>
            </u-form-item>
            <u-form-item :label-width="'180rpx'" label="单据日期" borderBottom>
              <u--input v-model="purchaseInfo.order_date" border="none" placeholder="请选择日期" inputAlign="right" disabled
                disabledColor="#fff"></u--input>
            </u-form-item>
            <u-form-item :label-width="'180rpx'" label="单据编号" borderBottom>
              <u--input v-model="purchaseInfo.order_id" border="none" placeholder="提交后自动生成" inputAlign="right" disabled
                disabledColor="#fff"></u--input>
            </u-form-item>
            <u-form-item :label-width="'180rpx'" label="预计到货日期" borderBottom required>
              <u--input v-model="purchaseInfo.expected_arrival_date" border="none" placeholder="请选择到货日期"
                inputAlign="right" disabled disabledColor="#fff" @tap="openDatePicker"></u--input>
            </u-form-item>
          </u--form>
        </view>
      </view>

      <!-- 商品清单 - 使用merchbill组件 -->
      <merch-bill :items="purchaseInfo.items" :total_actual_amount="purchaseInfo.total_actual_amount" :prohibitModification="isDisabled" @open-product-select="openProductSelect"
        @open-product-details="openProductDetails" @amount-change="handleAmountChange" />

      <!-- 结算信息 -->
      <view class="info">
        <view class="info_title">
          <text>结算信息</text>
        </view>
        <view class="form">
          <u--form labelPosition="left">
            <u-form-item :label-width="'180rpx'" label="结算账户" borderBottom :required="purchaseInfo.pay_amount > 0">
              <u-row>
                <u-col :span="purchaseInfo.payment_method_name ? 10.5 : 12">
                  <u--input v-model="purchaseInfo.payment_method_name" border="none" placeholder="请选择结算账户"
                    inputAlign="right" disabled disabledColor="#fff" @tap="openAccountSelector"
                    :required="purchaseGoodsInfo.pay_amount > 0"></u--input>
                </u-col>
                <u-col span="0.5"></u-col>
                <u-col span="1">
                  <view @click="removePurchaseOrder">
                    <i-close-one theme="filled" size="20" fill="#d13b3b" v-if="purchaseInfo.payment_method_name" />
                  </view>
                </u-col>
              </u-row>
            </u-form-item>
            <u-form-item :label-width="'180rpx'" label="支付预付款" borderBottom>
              <u--input v-model="purchaseInfo.pay_amount" border="none" placeholder="0" inputAlign="right"
                @blur="changePayAmount" :disabled="isDisabled"></u--input>
            </u-form-item>
          </u--form>
        </view>
      </view>

      <!-- 备注 -->
      <view class="info">
        <view class="info_title">
          <text>备注</text>
        </view>
        <view class="remark">
          <u--textarea v-model="purchaseInfo.remark" placeholder="请输入备注" autoHeight border="none"
            :disabled="isDisabled"></u--textarea>
        </view>
      </view>

      <!-- 附件信息 -->
      <imageUpload :prohibitModification="isDisabled" />
      <!-- 底部操作栏 -->
      <!-- 如果为部分到货/全部到货，即不可修改 -->
      <view class="operation" v-if="
        purchaseInfo.order_status !== 'partial' &&
        purchaseInfo.order_status !== 'completed'
      ">
        <u-button type="error" text="删除" v-if="purchaseInfo.id" @click="deletePurchaseOrder"></u-button>
        <u-button type="primary" text="保存" @click="savePurchaseOrder"></u-button>
      </view>
    </scroll-view>

    <!-- 语音输入组件 -->
    <view class="input-area">
      <Input style="height: 100%" :isShowCamera="true" />
    </view>

    <!-- 选择搜索器-供应商 -->
    <searchSelector :selectorShow.sync="selectorShow" :list="selectList" :selectType="selectType"
      @confirm="selectSupplier" @close="closePopup" @newSupplier="newSupplier"
      @supplierScrollToLower="getSupplierList" />
    <!-- 商品详细信息 -->
    <product-details :productDetailsShow.sync="productDetailsShow" :type="2" :productData="productData"
      :isShowDelBtn="true" :isShowUnit="false" @delBtnPricing="handleDelBtnPricing" @close="handleProductDetailsClose"
      @confirm="handleProductDetailsConfirm" />
    <!-- 日期选择器 -->
    <datePickers :showDatePicker="showDatePicker" @close="closeDatePicker" @confirm="handleDateConfirm" />

    <!-- 结算账户弹出层 -->
    <settlementAccount :accountSelectShow="accountSelectShow" @close="closeAccountSelector" @confirm="handleAccount" />
  </view>
</template>

<script>
import Input from "@/components/input/input.vue";
import goodsList from "@/components/goodsList.vue";
import searchSelector from "@/components/searchSelector.vue";
import productDetails from "@/components/productDetails.vue";
import imageUpload from "@/components/imageUpload/imageUpload.vue";
import settlementAccount from "@/components/settlementAccount/settlementAccount.vue";
import MerchBill from "@/components/merchbill.vue";
import { getSupplier } from "@/api/supplier";
import {
  createPurchaseOrder,
  updatePurchaseOrder,
  deletePurchaseOrder,
} from "@/api/purchaseOrder";
import eventBus from "@/utils/eventBus";
import datePickers from "@/components/datePickers.vue";

export default {
  components: {
    Input,
    goodsList,
    searchSelector,
    productDetails,
    imageUpload,
    datePickers,
    settlementAccount,
    MerchBill
  },
  data() {
    return {
      isDisabledTitle: '信息禁止修改',
      isDisabled: false,//是否禁用
      accountSelectShow: false, //结算账户弹出层
      isExpandGoodsList: true,
      showDatePicker: false, //日期选择器
      purchaseInfo: {
        supplier_name: "", // 供应商名称
        supplier: 0, // 供应商ID
        order_date: "", // 单据日期
        order_id: "", // 单据编号
        items: [], // 商品清单（数组）
        total_amount: "0", // 合计金额
        discountRate: "0", // 优惠率(%)
        discount: "0", // 优惠金额
        actual_purchase_price: "0", // 优惠后金额
        handler: "", // 经办人ID
        handler_name: "", // 经办人名称
        pay_amount: "0", // 预付款
        payment_method: "", // 付款方式
        payment_method_name: "", // 付款方式名称
        remark: "", // 备注
        attachments: [], // 附件列表
        expected_arrival_date: "", // 预计到货日期（格式化后显示用）
        debt: "0", // 本次欠款
      },
      rules: {
        "purchaseInfo.supplier_name": {
          type: "string",
          required: true,
          message: "请选择供应商",
          trigger: ["blur", "change"],
        },
        "purchaseInfo.expected_arrival_date": {
          type: "string",
          required: true,
          message: "请选择预计到货日期",
          trigger: ["blur", "change"],
        },
      },
      selectorShow: false,
      productData: {}, //商品详情
      productDetailsShow: false, //商品详情弹出层
      selectList: [], //搜索列表
      selectType: 0, //搜索类型
      supplierList: [], //供应商列表
      isSupplierMore: true, //是否还有更多供应商
      supplierPageParams: {
        page: 1,
        page_size: 20,
      },
      settlementAccountList: [],
      activeQtyIndex: -1,
    };
  },
  watch: {
    // 商品明细变化时自动联动
    "purchaseInfo.items": {
      handler() {
        // this.calcTotalAndDiscount();
      },
      deep: true,
      immediate: true,
    },
    // 优惠率变化时
    "purchaseInfo.discountRate": {
      handler(newVal) {
        if (newVal > 100) {
          uni.showToast({
            title: "优惠率不能大于100",
            icon: "none",
          });
          this.purchaseInfo.discountRate = 100;
          return;
        }
      },
    },
  },
  mounted() {
    this.getSupplierList();
    eventBus.$on("selectGoodsList", this.handleSelectGoodsList);
  },
  beforeDestroy() {
    eventBus.$off("selectGoodsList", this.handleSelectGoodsList);
  },
  onReady() {
    // 微信小程序需要用此写法
    this.$refs.datetimePicker.setFormatter(this.formatter);
  },
  methods: {
    //打开结算账户选择
    openAccountSelector() {
      if (this.isDisabled) {
        uni.showToast({
          title: this.isDisabledTitle,
          icon: 'none'
        })
        return;
      }
      this.accountSelectShow = true;
    },
    //删除结算账户
    removePurchaseOrder() {
      if (this.isDisabled) {
        uni.showToast({
          title: this.isDisabledTitle,
          icon: 'none'
        })
        return;
      }
      this.purchaseInfo.payment_method = "";
      this.purchaseInfo.payment_method_name = "";
    },
    //处理结算账户的数据
    handleAccount(data) {
      this.purchaseInfo.payment_method = data.id;
      this.purchaseInfo.payment_method_name = data.account_name;
    },
    //关闭结算账户选择
    closeAccountSelector() {
      this.accountSelectShow = false;
    },
    //商品清单删除商品
    handleDelBtnPricing(data) {
      this.purchaseInfo.items = this.purchaseInfo.items.filter(
        (item) => !(item.item === data.item && item.unit === data.unit)
      );
      this.productDetailsShow = false;
    },
    //删除订单
    deletePurchaseOrder() {
      deletePurchaseOrder(this.purchaseInfo).then((res) => {
        if (res.code == 0) {
          uni.showToast({
            title: "删除成功",
            icon: "none",
          });
          eventBus.$emit("refreshPurchaseOrders");
          uni.navigateBack();
        } else {
          uni.showToast({
            title: res.msg,
            icon: "none",
          });
        }
      });
    },

    //保存订单
    savePurchaseOrder() {
      //如果有预付款就要判断是否有结算账户
      if (
        Number(this.purchaseInfo.pay_amount) > 0 && !this.purchaseInfo.payment_method
      ) {
        uni.showToast({
          title: "请选择结算账户",
          icon: "none",
        });
        return;
      }

      if (this.purchaseInfo.id) {
        updatePurchaseOrder(this.purchaseInfo).then((res) => {
          if (res.code == 0) {
            uni.showToast({
              title: "保存成功",
              icon: "none",
            });
            eventBus.$emit("refreshPurchaseOrders");
            uni.navigateBack();
          } else {
            uni.showToast({
              title: res.msg,
              icon: "none",
            });
          }
        });
      } else {
        createPurchaseOrder(this.purchaseInfo).then((res) => {
          if (res.code == 0) {
            uni.showToast({
              title: "保存成功",
              icon: "none",
            });
            eventBus.$emit("refreshPurchaseOrders");
            uni.navigateBack();
          } else {
            uni.showToast({
              title: res.msg,
              icon: "none",
            });
          }
        });
      }
    },
    //选择商品
    handleSelectGoodsList(data) {
      console.log(data);

      // 创建新数组以强制更新
      const updatedItems = [...this.purchaseInfo.items];

      // 遍历选择的商品数组
      data.forEach(selectedItem => {
        // 查找是否存在相同的商品和单位类型
        const existingIndex = updatedItems.findIndex(
          (existingItem) => existingItem.item === selectedItem.item && existingItem.unit === selectedItem.unit
        );

        if (existingIndex !== -1) {
          // 如果找到相同的商品和单位类型，覆盖原来的数据
          updatedItems[existingIndex] = selectedItem;
        } else {
          // 如果没有找到，新增到items的后面
          updatedItems.push(selectedItem);
        }
      });

      // 强制更新整个数组
      this.$set(this.purchaseInfo, 'items', updatedItems);

      // this.calcTotalAndDiscount();
    },

    //打开日期选择器
    openDatePicker() {
      if (this.isDisabled) {
        uni.showToast({
          title: this.isDisabledTitle,
          icon: 'none'
        })
        return;
      }
      this.showDatePicker = true;
    },
    // 关闭日期选择器
    closeDatePicker() {
      this.showDatePicker = false;
    },
    // 日期选择器确认
    handleDateConfirm(date) {
      this.purchaseInfo.expected_arrival_date = date;
      this.showDatePicker = false;
    },

    //获取供应商列表
    getSupplierList() {
      if (!this.isSupplierMore) return;
      getSupplier(this.supplierPageParams).then((res) => {
        if (res.code == 0) {
          this.supplierList = res.data.results;
          if (this.supplierPageParams.page_size > res.data.results.length) {
            this.isSupplierMore = false;
          } else {
            this.supplierPageParams.page++;
            this.isSupplierMore = true;
          }
        } else {
          uni.showToast({
            title: res.msg,
            icon: "none",
          });
        }
      });
    },
    //打开搜索弹出层
    openSelector(index) {
      if (this.isDisabled) {
        uni.showToast({
          title: this.isDisabledTitle,
          icon: 'none'
        })
        return;
      }
      this.selectType = index;
      this.selectorShow = true;
      if (this.selectType === 0) {
        this.selectList = this.supplierList;
      } else {
        this.selectList = this.settlementAccountList;
      }
    },
    //关闭搜索弹出层
    closePopup() {
      this.selectorShow = false;
    },
    //选择供应商
    selectSupplier(item) {
      if (this.selectType === 0) {
        this.purchaseInfo.supplier = item.id;
        this.purchaseInfo.supplier_name = item.name;
      } else if (this.selectType === 1) {
        this.purchaseInfo.account = item.label;
      }
    },
    //新建供应商
    newSupplier(data) {
      this.list.unshift(data);
    },

    //打开商品详情弹出层
    openProductDetails(item) {
      console.log(item);
      if (this.isDisabled) {
        uni.showToast({
          title: this.isDisabledTitle,
          icon: 'none'
        })
        return;
      }
      this.productData = item;
      this.productDetailsShow = true;
    },
    //关闭商品详情弹出层
    handleProductDetailsClose() {
      this.productDetailsShow = false;
    },
    //商品详情弹出层确认
    handleProductDetailsConfirm(item) {
      this.productData = item;

      console.log(item);
      console.log(this.purchaseInfo.items);

      // 使用 findIndex 查找相同商品和单位类型的元素索引
      const index = this.purchaseInfo.items.findIndex(
        (existingItem) => existingItem.item === item.item && existingItem.unit === item.unit
      );

      if (index !== -1) {
        // 使用 splice 方法来替换元素，确保响应式更新
        this.purchaseInfo.items.splice(index, 1, item);
      }

      // this.calcTotalAndDiscount();
      this.productDetailsShow = false;
    },
    //跳转选择商品页面
    openProductSelect() {
      uni.navigateTo({
        url: "/components/productSelection" + "?type=2",
      });
    },
    // // 1. 计算总金额
    // calcTotalAmount() {
    //   let total = 0;
    //   if (
    //     Array.isArray(this.purchaseInfo.items) &&
    //     this.purchaseInfo.items.length > 0
    //   ) {
    //     this.purchaseInfo.items.forEach((item) => {
    //       if (
    //         item.purchase_price !== undefined &&
    //         item.quantity !== undefined
    //       ) {
    //         total += Number(item.purchase_price) * Number(item.quantity);
    //       }
    //     });
    //   }
    //   this.purchaseInfo.total_amount = total.toFixed(2);
    //   return total;
    // },

    // // 2. 根据优惠率计算优惠金额
    // calcDiscountAmountFromRate() {
    //   const total = Number(this.purchaseInfo.total_amount) || 0;
    //   const discountRate = Number(this.purchaseInfo.discountRate) || 0;
    //   this.purchaseInfo.discount = ((total * discountRate) / 100).toFixed(2);
    // },

    // // 3. 根据优惠金额计算优惠率
    // calcDiscountRateFromAmount() {
    //   const total = Number(this.purchaseInfo.total_amount) || 0;
    //   const discountAmount = Number(this.purchaseInfo.discount) || 0;
    //   if (total > 0) {
    //     this.purchaseInfo.discountRate = ((discountAmount / total) * 100).toFixed(2);
    //   } else {
    //     this.purchaseInfo.discountRate = "0";
    //   }
    // },

    // // 4. 计算优惠后金额
    // calcActualAmount() {
    //   const total = Number(this.purchaseInfo.total_amount) || 0;
    //   const discount = Number(this.purchaseInfo.discount) || 0;
    //   const actualAmount = total - discount;
    //   this.purchaseInfo.actual_purchase_price = actualAmount.toFixed(2);
    //   return actualAmount;
    // },

    // 5. 计算本次付款和欠款
    calcPayAndDebt() {
      const actualAmount = Number(this.purchaseInfo.actual_purchase_price) || 0;
      let payAmount = Number(this.purchaseInfo.pay_amount);
      if (
        isNaN(payAmount) ||
        !this.purchaseInfo.pay_amount ||
        this.purchaseInfo.pay_amount === "0"
      ) {
        payAmount = actualAmount;
        this.purchaseInfo.pay_amount = actualAmount.toFixed(2);
      }
      const debt = actualAmount - payAmount;
      this.purchaseInfo.debt = debt.toFixed(2);
    },

    // // 总控方法
    // calcTotalAndDiscount() {
    //   this.calcTotalAmount();
    //   this.calcActualAmount();
    //   this.calcPayAndDebt();
    // },

    // // 优惠率输入框失焦
    // changeDiscountRate(value) {
    //   this.purchaseInfo.discountRate = value;
    //   this.calcDiscountAmountFromRate();
    //   this.calcTotalAndDiscount();
    // },

    // // 优惠金额输入框失焦
    // changeDiscount(value) {
    //   this.purchaseInfo.discount = value;
    //   this.calcDiscountRateFromAmount();
    //   this.calcTotalAndDiscount();
    // },

    // // 本次付款输入框失焦
    // changePayAmount() {
    //   const actualAmount = Number(this.purchaseInfo.actual_purchase_price) || 0;
    //   const payAmount = Number(this.purchaseInfo.pay_amount) || 0;
    //   this.purchaseInfo.debt = (actualAmount - payAmount).toFixed(2);
    // },

    // 处理金额变化
    handleAmountChange(amountData) {
      this.purchaseInfo.total_amount = amountData.totalAmount;
      this.purchaseInfo.discount = amountData.discount;
      this.purchaseInfo.actual_purchase_price = amountData.actualPurchasePrice;
      // 重新计算付款和欠款
      this.calcPayAndDebt();
    },
  },
  onLoad(options) {
    console.log(options);
    
    if (options.data) {
      try {
        const item = JSON.parse(decodeURIComponent(options.data));
        // 这里可以根据实际结构做深拷贝或字段映射
        this.purchaseInfo = {
          ...this.purchaseInfo,
          ...item,
        };
        this.purchaseInfo.actual_purchase_price = item.total_actual_amount;

        if (data.order_status == 'partial' || data.order_status == 'completed') {
          this.isDisabled = true;
        }
      } catch (e) {
        console.error("解析采购单数据失败", e);
      }
    }

    if (options) {
      const date = new Date();
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      const day = date.getDate();
      this.purchaseInfo.order_date = `${year}-${month}-${day}`;
    }
  },
};
</script>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

.content {
  flex: 1;
  overflow: auto;
}

.info {
  background-color: #fff;
  margin-bottom: 20rpx;
}

.info_title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  border-bottom: 1px solid #eee;
  font-size: 28rpx;

  .sub-title {
    font-size: 24rpx;
    color: #999;
  }

  .arrow {
    transition: transform 0.3s;

    &.arrow-up {
      transform: rotate(180deg);
    }
  }
}

.form {
  padding: 0 20rpx;
}

.goods-list {
  padding: 20rpx;
}

.goods_item {
  border-bottom: 2px dashed #ccc;
  background: #fff;
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.03);
  margin-bottom: 20rpx;
  position: relative;
}

.goods_item_click {
  display: flex;
  align-items: center;
  width: 100%;
  background: #fff;
  padding: 10px 0;
}

.goods_img {
  width: 48px;
  height: 48px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.goods_img image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.goods_main {
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex: 1;
}

.goods_name {
  font-size: 16px;
  font-weight: bold;
  color: #222;
}

.goods_id {
  font-size: 12px;
  color: #888;
  margin: 2px 0;
}

.goods_price {
  display: flex;
  align-items: center;
  margin-top: 2px;
}

.price {
  color: #e22;
  font-size: 14px;
  font-weight: bold;
  margin-right: 2px;
}

.unit {
  color: #e22;
  font-size: 12px;
}



.qty_control {
  display: flex;
  align-items: center;
  height: 24px;
  position: relative;
}

.qty_input {
  width: 60px;
  height: 28px;
  border: 1px solid #bbb;
  border-radius: 6px;
  text-align: center;
  font-size: 18px;
  background: #fff;
  color: #222;
  margin: 0 4px;
}

i-left-one,
i-right-one {
  cursor: pointer;
  margin: 0 4px;
}

.arrow {
  font-size: 18px;
  color: #888;
  margin-left: 10px;
}

.goods_info {
  display: flex;

  .goods_attributes {
    width: 50%;
    font-size: 24rpx;
    display: flex;
    align-content: flex-start;
    align-items: flex-start;
    flex-direction: column;

    .goods_attribute {
      display: flex;
      margin-top: 10rpx;
    }
  }
}

.goods_attribute_name {
  width: 60px;
  display: inline-block;
  text-align-last: justify;
  text-align: justify;
  text-justify: distribute-all-lines;
}

.total-info {
  font-size: 28rpx;
}

.total-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10rpx;
  padding: 10rpx 0;
  border-bottom: 1px solid #eee;

  text:nth-child(1) {
    width: 80px;
    display: inline-block;
    text-align-last: justify;
    text-align: justify;
    text-justify: distribute-all-lines;
  }
}

.remark {
  padding: 20rpx;
}

.upload-area {
  padding: 20rpx;
}

.add-icon {
  padding: 10rpx;
  cursor: pointer;
}

.telescoping {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 40rpx;
  background-color: #fff;
}

.operation {
  width: 90%;
  height: 80rpx;
  margin: 10rpx auto;
  padding: 10rpx 20rpx;
  background-color: #fff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}

::v-deep .operation .u-button {
  height: 30px;
  width: 25%;
  position: relative;
  align-items: center;
  justify-content: center;
  display: flex;
  flex-direction: row;
  box-sizing: border-box;
  flex-direction: row;
}

::v-deep .u-input--radius.data-v-fdbb9fe6,
.u-input--square.data-v-fdbb9fe6 {
  border-radius: 4px;
  padding-top: 0 !important;
  padding-left: 0 !important;
  padding-bottom: 0 !important;
  padding-right: 0 !important;
}

::v-deep .u-input__content__field-wrapper__field.data-v-fdbb9fe6 {
  line-height: 26px;
  text-align: left;
  color: #303133;
  height: 24px;
  font-size: 15px;
  flex: 1;
  width: 125rpx;
}
</style>
