{"id": "icon-park", "displayName": "彩色svg图标，全平台兼容，支持vue，nvue，uvue，app，小程序，h5", "version": "1.0.8", "description": "5000+高质量开源图标，包括线框、填充、双色、多色4个主题，随意配置线条粗细等", "keywords": ["icon", "nvue", "彩色图标", "字节跳动", "彩色字体图标"], "repository": "", "engines": {"HBuilderX": "^3.1.0"}, "dcloudext": {"type": "component-vue", "sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "无", "permissions": "无"}, "npmurl": ""}, "uni_modules": {"dependencies": [], "encrypt": [], "platforms": {"cloud": {"tcb": "y", "aliyun": "y", "alipay": "n"}, "client": {"Vue": {"vue2": "y", "vue3": "y"}, "App": {"app-vue": "y", "app-nvue": "y", "app-uvue": "y"}, "H5-mobile": {"Safari": "y", "Android Browser": "y", "微信浏览器(Android)": "y", "QQ浏览器(Android)": "y"}, "H5-pc": {"Chrome": "y", "IE": "y", "Edge": "y", "Firefox": "y", "Safari": "y"}, "小程序": {"微信": "y", "阿里": "y", "百度": "u", "字节跳动": "y", "QQ": "y", "钉钉": "y", "快手": "u", "飞书": "y", "京东": "u"}, "快应用": {"华为": "u", "联盟": "u"}}}}}